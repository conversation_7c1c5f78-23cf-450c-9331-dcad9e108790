# PLM编码规则引擎 - 缓存和API文档功能

本文档介绍了新增的本地缓存和Swagger API文档功能。

## 🚀 新增功能

### 1. 本地缓存 (Caffeine)

#### 功能特性
- 使用 Caffeine 高性能本地缓存
- 自动缓存编码规则查询结果
- 支持缓存统计和监控
- 提供缓存管理API

#### 缓存配置
- **初始容量**: 100
- **最大容量**: 1000 条目
- **访问后过期**: 30分钟
- **写入后过期**: 60分钟
- **统计功能**: 已启用

#### 缓存的数据
- `codingRules`: 编码规则数据
  - `all`: 所有编码规则
  - `{id}`: 按ID缓存的编码规则
  - `objectType:{type}`: 按对象类型缓存的编码规则

#### 缓存管理API
```
GET  /api/cache/stats     - 获取所有缓存统计信息
GET  /api/cache/names     - 获取缓存名称列表
DELETE /api/cache/{name}  - 清空指定缓存
DELETE /api/cache/all     - 清空所有缓存
```

### 2. SpringDoc OpenAPI文档

#### 访问地址
- **Swagger UI**: http://localhost:8080/swagger-ui.html
- **API文档JSON**: http://localhost:8080/api-docs

#### 功能特性
- 完整的OpenAPI 3.0文档
- 在线API测试
- 请求/响应示例
- 参数说明和验证
- 更好的Spring Boot兼容性

#### 已文档化的API模块
1. **编码规则管理** (`/api/rules`)
   - 编码规则的增删改查
   - 代码生成和验证

2. **PLM编码规则引擎** (`/api`)
   - 编码规则管理
   - 物料分类管理
   - 代码生成和测试

3. **缓存管理** (`/api/cache`)
   - 缓存状态监控
   - 缓存清理操作

## 🔧 配置说明

### application.properties 新增配置

```properties
# SpringDoc OpenAPI Configuration
springdoc.api-docs.path=/api-docs
springdoc.swagger-ui.path=/swagger-ui.html
springdoc.swagger-ui.enabled=true
springdoc.packages-to-scan=com.plm.coding.controller

# Cache Configuration
spring.cache.type=caffeine
spring.cache.caffeine.spec=maximumSize=1000,expireAfterAccess=30m,expireAfterWrite=60m
```

## 📊 性能优化

### 缓存策略
1. **查询缓存**: 
   - `@Cacheable` - 缓存查询结果
   - 减少数据库访问次数

2. **更新清理**:
   - `@CacheEvict` - 数据更新时清理缓存
   - 保证数据一致性

3. **缓存监控**:
   - 命中率统计
   - 性能指标监控

### 预期性能提升
- 编码规则查询响应时间减少 60-80%
- 数据库查询次数减少 70-90%
- 系统整体响应速度提升

## 🛠️ 使用示例

### 1. 查看缓存状态
```bash
curl -X GET "http://localhost:8080/api/cache/stats"
```

### 2. 清空编码规则缓存
```bash
curl -X DELETE "http://localhost:8080/api/cache/codingRules"
```

### 3. 访问API文档
在浏览器中打开: http://localhost:8080/swagger-ui.html

## 📝 注意事项

1. **缓存一致性**: 数据更新操作会自动清理相关缓存
2. **内存使用**: 缓存会占用JVM内存，建议监控内存使用情况
3. **集群部署**: 当前为本地缓存，集群环境下需要考虑缓存同步
4. **Swagger安全**: 生产环境建议禁用Swagger UI或添加访问控制

## 🔍 故障排除

### 常见问题
1. **Swagger UI无法访问**: 检查SpringDoc配置和访问路径
2. **缓存不生效**: 确认 `@EnableCaching` 注解已启用
3. **内存占用过高**: 调整缓存大小配置
4. **依赖冲突**: 确保使用SpringDoc而不是Springfox

### 日志监控
```properties
# 启用缓存相关日志
logging.level.org.springframework.cache=DEBUG
logging.level.com.github.benmanes.caffeine=DEBUG
```
