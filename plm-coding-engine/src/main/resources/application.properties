server.port=8080
# MySQL Database Configuration
spring.datasource.url=*****************************************************************************************************************
spring.datasource.username=root
spring.datasource.password=123456
spring.datasource.driver-class-name=com.mysql.cj.jdbc.Driver
spring.jpa.database-platform=org.hibernate.dialect.MySQL8Dialect
spring.jpa.hibernate.ddl-auto=update
spring.jpa.show-sql=true
spring.jpa.properties.hibernate.format_sql=true
spring.jpa.open-in-view=false
# CORS Configuration for frontend
spring.web.cors.enabled=true
spring.web.cors.allowed-origins=http://localhost:8081 # Vue.js development server port
spring.web.cors.allowed-methods=GET,POST,PUT,DELETE,OPTIONS
spring.web.cors.allowed-headers=*
spring.web.cors.allow-credentials=true
logging.level.web=debug
logging.level.root=INFO
logging.level.com.plm.coding=DEBUG
logging.pattern.console=%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n
coding.rule.check-digit.enabled=true
coding.rule.sequence.cache-size=20
#########################
# enovia context
#########################
enovia.enabled=true
enovia.host=https://r2023x.mydomain.com/3dspace
enovia.user=admin_platform
enovia.password=Qwerty12345
enovia.vault=eService Production
enovia.role=
enovia.use3DPassport=true
enovia.useCertificates=false
#########################
# SpringDoc OpenAPI Configuration
#########################
springdoc.api-docs.path=/api-docs
springdoc.swagger-ui.path=/swagger-ui.html
springdoc.swagger-ui.enabled=true
springdoc.packages-to-scan=com.plm.coding.controller
#########################
# Cache Configuration
#########################
spring.cache.type=caffeine
spring.cache.caffeine.spec=maximumSize=1000,expireAfterAccess=30m,expireAfterWrite=60m

#########################
# Enovia Data Sync Configuration
#########################
enovia.sync.enabled=true
enovia.sync.auto-start=false
enovia.sync.batch-size=100
enovia.sync.retry-count=3
enovia.sync.retry-delay=5000

# ??????
enovia.sync.types.enabled=true
enovia.sync.types.include=Part,Document,ECO
enovia.sync.types.exclude=

# ??????
enovia.sync.classifications.enabled=true
enovia.sync.classifications.include=
enovia.sync.classifications.exclude=

# ??????
enovia.sync.attributes.enabled=true
enovia.sync.attributes.include=
enovia.sync.attributes.exclude=

# ????
enovia.sync.strategy=incremental
enovia.sync.full-sync-interval=24h
enovia.sync.incremental-interval=1h