package com.plm.coding.controller;

import com.plm.coding.model.*;
import com.plm.coding.repository.*;
import com.plm.coding.service.RuleEngineService;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;

import javax.persistence.EntityManager;
import java.time.LocalDateTime;
import java.util.List;
import java.util.stream.Collectors;

/**
 * RESTful API controller for managing coding rules, classifications, attributes, and sequences.
 * Also provides endpoints for testing and generating material codes using the custom rule engine.
 */
@Tag(name = "PLM编码规则引擎")
@RestController
@RequestMapping("/api")
public class RuleController {

    @Autowired
    private EntityManager entityManager;

    @Autowired
    private RuleEngineService ruleEngineService; // Inject custom rule engine service

    @Autowired
    private CodingRuleRepository codingRuleRepository; // Inject CodingRuleRepository

    @Autowired
    private CodingRuleSegmentRepository codingRuleSegmentRepository; // Inject CodingRuleSegmentRepository

    @Autowired
    private MaterialClassificationRepository classificationRepository; // Inject MaterialClassificationRepository

    @Autowired
    private ClassificationAttrDefRepository attrDefRepository; // Inject ClassificationAttrDefRepository

    @Autowired
    private SequenceConfigRepository sequenceConfigRepository; // Inject SequenceConfigRepository

    // --- Coding Rule APIs ---
    @GetMapping("/coding-rules")
    public ResponseEntity<List<CodingRule>> getAllCodingRules() {
        return ResponseEntity.ok(codingRuleRepository.findAll());
    }

    @GetMapping("/coding-rules/{id}")
    public ResponseEntity<CodingRule> getCodingRuleById(
        @PathVariable Long id
    ) {
        return codingRuleRepository.findById(id).map(ResponseEntity::ok).orElse(ResponseEntity.notFound().build());
    }
    @PostMapping("/coding-rules")
    @Transactional
    public ResponseEntity<CodingRule> createCodingRule(
        @RequestBody CodingRule rule
    ) {
        // Ensure segments have correct rule reference and order
        if (rule.getSegments() != null) {
            for (int i = 0; i < rule.getSegments().size(); i++) {
                CodingRuleSegment segment = rule.getSegments().get(i);
                segment.setId(null); // Ensure new IDs for new segments to be persisted
                segment.setRule(rule);
                segment.setSegmentOrder(i + 1); // Set order based on list index
            }
        }
        CodingRule savedRule = codingRuleRepository.save(rule);
        return ResponseEntity.status(HttpStatus.CREATED).body(savedRule);
    }

    /**
     * Update an existing coding rule.
     *
     * @param id          The ID of the coding rule to update.
     * @param updatedRule The updated CodingRule object.
     * @return Updated CodingRule object.
     */
    @PutMapping("/coding-rules/{id}")
    @Transactional
    public ResponseEntity<CodingRule> updateCodingRule(@PathVariable Long id, @RequestBody CodingRule updatedRule) {
        return codingRuleRepository.findById(id)
                .map(existingRule -> {
                    // Update main entity attributes
                    existingRule.setRuleName(updatedRule.getRuleName());
                    existingRule.setDescription(updatedRule.getDescription());
                    existingRule.setObjectTypeCode(updatedRule.getObjectTypeCode());
                    existingRule.setPriority(updatedRule.getPriority());
                    existingRule.setEnableCheckDigit(updatedRule.getEnableCheckDigit());
                    existingRule.setIsActive(updatedRule.getIsActive());
                    existingRule.setLastModifiedAt(LocalDateTime.now());
                    existingRule.setLastModifiedBy("admin");

                    // Handle segments collection properly
                    if (existingRule.getSegments() != null) {
                        // Clear existing segments - this will trigger orphan removal
                        existingRule.getSegments().clear();

                        // Flush to ensure orphans are deleted before adding new ones
                        entityManager.flush();
                    }

                    // Add new segments
                    if (updatedRule.getSegments() != null) {
                        for (int i = 0; i < updatedRule.getSegments().size(); i++) {
                            CodingRuleSegment segment = updatedRule.getSegments().get(i);
                            segment.setId(null); // Ensure new entity
                            segment.setRule(existingRule); // Set parent reference
                            segment.setSegmentOrder(i + 1); // Set order

                            // Add to the existing collection
                            existingRule.getSegments().add(segment);
                        }
                    }

                    // Save and return
                    return ResponseEntity.ok(codingRuleRepository.save(existingRule));
                })
                .orElse(ResponseEntity.notFound().build());
    }

    /**
     * Delete a coding rule by ID.
     *
     * @param id The ID of the coding rule to delete.
     * @return 204 No Content if successful, otherwise 404.
     */
    @DeleteMapping("/coding-rules/{id}")
    @Transactional
    public ResponseEntity<Void> deleteCodingRule(@PathVariable Long id) {
        if (codingRuleRepository.existsById(id)) {
            // With orphanRemoval=true on segments, deleting the parent rule will cascade delete its segments.
            // Explicitly deleting segments first is generally not needed if orphanRemoval is set up correctly,
            // but can be kept as a safeguard if cascade delete behavior is inconsistent.
            // For this specific error, it's not the root cause, but also not harmful.
            codingRuleRepository.deleteById(id);
            return ResponseEntity.noContent().build();
        }
        return ResponseEntity.notFound().build();
    }

    // --- Material Code Generation/Test APIs ---
    @PostMapping("/generate-code/test")
    public ResponseEntity<TestResult> testGenerateCode(
        @RequestBody TestRequest request
    ) {
        MaterialFact materialFact =
                new MaterialFact(request.getObjectTypeCode(), request.getClassificationCode(), request.getAttributes());

        // 临时创建 CodingRule 对象
        CodingRule testRule = new CodingRule();
        testRule.setRuleName(request.getRule().getRuleName());
        testRule.setObjectTypeCode(request.getRule().getObjectTypeCode());
        testRule.setEnableCheckDigit(request.getRule().getEnableCheckDigit());
        // Map segments from request to actual CodingRuleSegment entities
        List<CodingRuleSegment> testSegments = request.getRule().getSegments().stream().map(seg -> {
            CodingRuleSegment newSeg = new CodingRuleSegment();
            newSeg.setSegmentOrder(seg.getSegmentOrder());
            newSeg.setSegmentType(seg.getSegmentType());
            newSeg.setFixedValue(seg.getFixedValue());
            newSeg.setTimeFormat(seg.getTimeFormat());
            newSeg.setAttributeCode(seg.getAttributeCode());
            newSeg.setClassificationCode(seg.getClassificationCode());
            newSeg.setSequenceName(seg.getSequenceName());
            newSeg.setLength(seg.getLength());
            newSeg.setPaddingChar(seg.getPaddingChar());
            newSeg.setPaddingDirection(seg.getPaddingDirection());
            newSeg.setTruncateDirection(seg.getTruncateDirection());
            newSeg.setDescription(seg.getDescription());
            newSeg.setPrefix(seg.getPrefix());
            newSeg.setSuffix(seg.getSuffix());
            newSeg.setRule(testRule); // Link to the temporary rule
            return newSeg;
        }).collect(Collectors.toList());
        testRule.setSegments(testSegments); // Set segments for the test rule

        // 用预览方法生成编码，顺序码不落库
        MaterialFact resultFact = ruleEngineService.generateCodePreview(testRule, materialFact);

        TestResult result = new TestResult();
        result.setGeneratedCode(resultFact.getGeneratedCode());
        result.setSegments(resultFact.getSegments());
        return ResponseEntity.ok(result);
    }

    @PostMapping("/generate-code")
    @Transactional
    public ResponseEntity<MaterialFact> generateCode(
        @RequestBody MaterialFact materialFact
    ) {
        MaterialFact generatedFact = ruleEngineService.generateCode(materialFact);
        // TODO: Persist generatedFact to GENERATED_MATERIAL_CODES and MATERIAL_MASTER if needed
        return ResponseEntity.ok(generatedFact);
    }

    // --- Material Classification APIs --- (No change)
    @GetMapping("/classifications")
    public ResponseEntity<List<MaterialClassification>> getAllClassifications() {
        return ResponseEntity.ok(classificationRepository.findAll());
    }

    @GetMapping("/classifications/{id}")
    public ResponseEntity<MaterialClassification> getClassificationById(@PathVariable Long id) {
        return classificationRepository.findById(id).map(ResponseEntity::ok).orElse(ResponseEntity.notFound().build());
    }

    @PostMapping("/classifications")
    public ResponseEntity<MaterialClassification> createClassification(@RequestBody MaterialClassification classification) {
        return ResponseEntity.status(HttpStatus.CREATED).body(classificationRepository.save(classification));
    }

    @PutMapping("/classifications/{id}")
    public ResponseEntity<MaterialClassification> updateClassification(@PathVariable Long id, @RequestBody MaterialClassification classification) {
        return classificationRepository.findById(id).map(existing -> {
            existing.setName(classification.getName());
            existing.setCode(classification.getCode());
            existing.setParentId(classification.getParentId());
            existing.setDescription(classification.getDescription());
            return ResponseEntity.ok(classificationRepository.save(existing));
        }).orElse(ResponseEntity.notFound().build());
    }

    @DeleteMapping("/classifications/{id}")
    public ResponseEntity<Void> deleteClassification(@PathVariable Long id) {
        if (classificationRepository.existsById(id)) {
            classificationRepository.deleteById(id);
            return ResponseEntity.noContent().build();
        }
        return ResponseEntity.notFound().build();
    }

    // --- Classification Attribute Definition APIs --- (No change)
    @GetMapping("/classifications/{classificationId}/attributes")
    public ResponseEntity<List<ClassificationAttrDef>> getAttributesByClassification(@PathVariable Long classificationId) {
        return ResponseEntity.ok(attrDefRepository.findByClassificationId(classificationId));
    }

    @PostMapping("/classification-attributes")
    public ResponseEntity<ClassificationAttrDef> createClassificationAttribute(@RequestBody ClassificationAttrDef attrDef) {
        return ResponseEntity.status(HttpStatus.CREATED).body(attrDefRepository.save(attrDef));
    }

    // --- Sequence Config APIs --- (No change)
    @GetMapping("/sequences")
    public ResponseEntity<List<SequenceConfig>> getAllSequenceConfigs() {
        return ResponseEntity.ok(sequenceConfigRepository.findAll());
    }

    @GetMapping("/sequences/{name}")
    public ResponseEntity<SequenceConfig> getSequenceConfigByName(@PathVariable String name) {
        return sequenceConfigRepository.findBySequenceName(name).map(ResponseEntity::ok)
                .orElse(ResponseEntity.notFound().build());
    }

    @PostMapping("/sequences")
    public ResponseEntity<SequenceConfig> createSequenceConfig(@RequestBody SequenceConfig config) {
        if (config.getInitialValue() == null) {
            config.setInitialValue(config.getCurrentValue());
        }
        if (config.getIncrementBy() == null) {
            config.setIncrementBy(1); // 默认步长
        }
        return ResponseEntity.status(HttpStatus.CREATED).body(sequenceConfigRepository.save(config));
    }

    @PutMapping("/sequences/{name}")
    public ResponseEntity<SequenceConfig> updateSequenceConfig(@PathVariable String name, @RequestBody SequenceConfig config) {
        return sequenceConfigRepository.findBySequenceName(name).map(existing -> {
            existing.setCurrentValue(config.getCurrentValue());
            existing.setPrefix(config.getPrefix());
            existing.setSuffix(config.getSuffix());
            existing.setIncrementBy(config.getIncrementBy());
            existing.setInitialValue(config.getInitialValue());
            return ResponseEntity.ok(sequenceConfigRepository.save(existing));
        }).orElse(ResponseEntity.notFound().build());
    }

    @DeleteMapping("/sequences/{name}")
    public ResponseEntity<Void> deleteSequenceConfig(@PathVariable String name) {
        if (sequenceConfigRepository.existsById(name)) {
            sequenceConfigRepository.deleteById(name);
            return ResponseEntity.noContent().build();
        }
        return ResponseEntity.notFound().build();
    }
}
