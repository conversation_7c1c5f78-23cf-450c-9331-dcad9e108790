package com.plm.coding.controller;

import com.github.benmanes.caffeine.cache.stats.CacheStats;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.CacheManager;
import org.springframework.cache.caffeine.CaffeineCache;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.Map;

/**
 * 缓存管理控制器
 * 提供缓存状态查询和管理功能
 */
@Tag(name = "缓存管理", description = "缓存状态查询和管理功能")
@RestController
@RequestMapping("/api/cache")
public class CacheController {

    @Autowired
    private CacheManager cacheManager;

    @Operation(summary = "获取所有缓存状态", description = "获取系统中所有缓存的统计信息")
    @ApiResponses({
        @ApiResponse(responseCode = "200", description = "成功获取缓存状态")
    })
    @GetMapping("/stats")
    public ResponseEntity<Map<String, Object>> getCacheStats() {
        Map<String, Object> stats = new HashMap<>();
        
        cacheManager.getCacheNames().forEach(cacheName -> {
            org.springframework.cache.Cache cache = cacheManager.getCache(cacheName);
            if (cache instanceof CaffeineCache) {
                CaffeineCache caffeineCache = (CaffeineCache) cache;
                com.github.benmanes.caffeine.cache.Cache<Object, Object> nativeCache = 
                    caffeineCache.getNativeCache();
                CacheStats cacheStats = nativeCache.stats();
                
                Map<String, Object> cacheInfo = new HashMap<>();
                cacheInfo.put("size", nativeCache.estimatedSize());
                cacheInfo.put("hitCount", cacheStats.hitCount());
                cacheInfo.put("missCount", cacheStats.missCount());
                cacheInfo.put("hitRate", cacheStats.hitRate());
                cacheInfo.put("evictionCount", cacheStats.evictionCount());
                cacheInfo.put("loadCount", cacheStats.loadCount());
                cacheInfo.put("averageLoadTime", cacheStats.averageLoadPenalty());
                
                stats.put(cacheName, cacheInfo);
            }
        });
        
        return ResponseEntity.ok(stats);
    }

    @Operation(summary = "清空指定缓存", description = "清空指定名称的缓存中的所有数据")
    @ApiResponses({
        @ApiResponse(responseCode = "200", description = "成功清空缓存"),
        @ApiResponse(responseCode = "404", description = "缓存不存在")
    })
    @DeleteMapping("/{cacheName}")
    public ResponseEntity<String> clearCache(
        @Parameter(description = "缓存名称", required = true) @PathVariable String cacheName
    ) {
        org.springframework.cache.Cache cache = cacheManager.getCache(cacheName);
        if (cache != null) {
            cache.clear();
            return ResponseEntity.ok("Cache '" + cacheName + "' cleared successfully");
        } else {
            return ResponseEntity.notFound().build();
        }
    }

    @Operation(summary = "清空所有缓存", description = "清空系统中所有缓存的数据")
    @ApiResponses({
        @ApiResponse(responseCode = "200", description = "成功清空所有缓存")
    })
    @DeleteMapping("/all")
    public ResponseEntity<String> clearAllCaches() {
        cacheManager.getCacheNames().forEach(cacheName -> {
            org.springframework.cache.Cache cache = cacheManager.getCache(cacheName);
            if (cache != null) {
                cache.clear();
            }
        });
        return ResponseEntity.ok("All caches cleared successfully");
    }

    @Operation(summary = "获取缓存名称列表", description = "获取系统中所有缓存的名称列表")
    @ApiResponses({
        @ApiResponse(responseCode = "200", description = "成功获取缓存名称列表")
    })
    @GetMapping("/names")
    public ResponseEntity<java.util.Collection<String>> getCacheNames() {
        return ResponseEntity.ok(cacheManager.getCacheNames());
    }
}
