package com.plm.coding.controller;

import com.plm.coding.integration.sync.EnoviaDataSyncService;
import com.plm.coding.integration.sync.EnoviaSyncProperties;
import com.plm.coding.model.EnoviaSyncRecord;
import com.plm.coding.model.EnoviaType;
import com.plm.coding.repository.EnoviaSyncRecordRepository;
import com.plm.coding.repository.EnoviaTypeRepository;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.concurrent.CompletableFuture;

/**
 * Enovia 数据同步控制器
 * 提供数据同步相关的 REST API
 */
@Slf4j
@RestController
@RequestMapping("/api/enovia/sync")
@Tag(name = "Enovia数据同步")
public class EnoviaSyncController {
    
    @Autowired
    private EnoviaDataSyncService syncService;
    
    @Autowired
    private EnoviaSyncProperties syncProperties;
    
    @Autowired
    private EnoviaSyncRecordRepository syncRecordRepository;
    
    @Autowired
    private EnoviaTypeRepository enoviaTypeRepository;
    
    /**
     * 获取同步配置
     */
    @GetMapping("/config")
    public ResponseEntity<EnoviaSyncProperties> getSyncConfig() {
        return ResponseEntity.ok(syncProperties);
    }
    
    /**
     * 启动全量同步
     */
    @PostMapping("/full")
    public ResponseEntity<String> startFullSync() {
        try {
            CompletableFuture<EnoviaSyncRecord> future = syncService.performFullSync();
            return ResponseEntity.ok("全量同步已启动，任务ID: " + future.hashCode());
        } catch (Exception e) {
            log.error("启动全量同步失败", e);
            return ResponseEntity.internalServerError().body("启动全量同步失败: " + e.getMessage());
        }
    }
    
    /**
     * 启动增量同步
     */
    @PostMapping("/incremental")
    public ResponseEntity<String> startIncrementalSync() {
        try {
            CompletableFuture<EnoviaSyncRecord> future = syncService.performIncrementalSync();
            return ResponseEntity.ok("增量同步已启动，任务ID: " + future.hashCode());
        } catch (Exception e) {
            log.error("启动增量同步失败", e);
            return ResponseEntity.internalServerError().body("启动增量同步失败: " + e.getMessage());
        }
    }
    
    /**
     * 获取同步记录列表
     */
    @GetMapping("/records")
    public ResponseEntity<Page<EnoviaSyncRecord>> getSyncRecords(
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "20") int size,
            @RequestParam(required = false) EnoviaSyncRecord.SyncType syncType,
            @RequestParam(required = false) EnoviaSyncRecord.SyncStatus syncStatus) {
        
        Pageable pageable = PageRequest.of(page, size, Sort.by("startTime").descending());
        Page<EnoviaSyncRecord> records;
        
        if (syncType != null && syncStatus != null) {
            records = syncRecordRepository.findBySyncTypeAndSyncStatus(syncType, syncStatus, pageable);
        } else if (syncType != null) {
            records = syncRecordRepository.findBySyncType(syncType, pageable);
        } else if (syncStatus != null) {
            records = syncRecordRepository.findBySyncStatus(syncStatus, pageable);
        } else {
            records = syncRecordRepository.findAll(pageable);
        }
        
        return ResponseEntity.ok(records);
    }
    
    /**
     * 获取同步记录详情
     */
    @GetMapping("/records/{id}")
    public ResponseEntity<EnoviaSyncRecord> getSyncRecord(@PathVariable Long id) {
        return syncRecordRepository.findById(id)
            .map(ResponseEntity::ok)
            .orElse(ResponseEntity.notFound().build());
    }
    
    /**
     * 获取正在运行的同步任务
     */
    @GetMapping("/running")
    public ResponseEntity<List<EnoviaSyncRecord>> getRunningTasks() {
        List<EnoviaSyncRecord.SyncStatus> runningStatuses = List.of(
            EnoviaSyncRecord.SyncStatus.PENDING,
            EnoviaSyncRecord.SyncStatus.RUNNING
        );
        List<EnoviaSyncRecord> runningTasks = syncRecordRepository.findBySyncStatusIn(runningStatuses);
        return ResponseEntity.ok(runningTasks);
    }
    
    /**
     * 获取同步统计信息
     */
    @GetMapping("/statistics")
    public ResponseEntity<SyncStatistics> getSyncStatistics() {
        SyncStatistics stats = new SyncStatistics();
        
        // 统计各类型的同步记录数量
        stats.setTotalSyncRecords(syncRecordRepository.count());
        stats.setSuccessfulSyncs(syncRecordRepository.countBySyncTypeAndStatus(
            EnoviaSyncRecord.SyncType.ALL, EnoviaSyncRecord.SyncStatus.SUCCESS));
        stats.setFailedSyncs(syncRecordRepository.countBySyncTypeAndStatus(
            EnoviaSyncRecord.SyncType.ALL, EnoviaSyncRecord.SyncStatus.FAILED));
        
        // 统计同步的数据量
        stats.setTotalTypes(enoviaTypeRepository.count());
        stats.setSyncedTypes(enoviaTypeRepository.countBySyncStatus(EnoviaType.SyncStatus.SUCCESS));
        
        // 获取最后一次成功同步记录
        syncRecordRepository.findLastSuccessfulSync(EnoviaSyncRecord.SyncType.ALL)
            .ifPresent(record -> {
                stats.setLastSuccessfulSync(record.getEndTime());
                stats.setLastSyncDuration(
                    java.time.Duration.between(record.getStartTime(), record.getEndTime()).toMillis()
                );
            });
        
        return ResponseEntity.ok(stats);
    }
    
    /**
     * 清除同步缓存
     */
    @PostMapping("/clear-cache")
    public ResponseEntity<String> clearCache() {
        try {
            syncService.clearCache();
            return ResponseEntity.ok("缓存已清除");
        } catch (Exception e) {
            log.error("清除缓存失败", e);
            return ResponseEntity.internalServerError().body("清除缓存失败: " + e.getMessage());
        }
    }
    
    /**
     * 获取 Enovia 类型列表
     */
    @GetMapping("/types")
    public ResponseEntity<Page<EnoviaType>> getEnoviaTypes(
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "20") int size,
            @RequestParam(required = false) EnoviaType.SyncStatus syncStatus) {
        
        Pageable pageable = PageRequest.of(page, size, Sort.by("typeName"));
        Page<EnoviaType> types;
        
        if (syncStatus != null) {
            types = enoviaTypeRepository.findBySyncStatus(syncStatus, pageable);
        } else {
            types = enoviaTypeRepository.findAll(pageable);
        }
        
        return ResponseEntity.ok(types);
    }
    
    /**
     * 同步统计信息 DTO
     */
    public static class SyncStatistics {
        private Long totalSyncRecords;
        private Long successfulSyncs;
        private Long failedSyncs;
        private Long totalTypes;
        private Long syncedTypes;
        private java.time.LocalDateTime lastSuccessfulSync;
        private Long lastSyncDuration; // 毫秒
        
        // Getters and Setters
        public Long getTotalSyncRecords() { return totalSyncRecords; }
        public void setTotalSyncRecords(Long totalSyncRecords) { this.totalSyncRecords = totalSyncRecords; }
        
        public Long getSuccessfulSyncs() { return successfulSyncs; }
        public void setSuccessfulSyncs(Long successfulSyncs) { this.successfulSyncs = successfulSyncs; }
        
        public Long getFailedSyncs() { return failedSyncs; }
        public void setFailedSyncs(Long failedSyncs) { this.failedSyncs = failedSyncs; }
        
        public Long getTotalTypes() { return totalTypes; }
        public void setTotalTypes(Long totalTypes) { this.totalTypes = totalTypes; }
        
        public Long getSyncedTypes() { return syncedTypes; }
        public void setSyncedTypes(Long syncedTypes) { this.syncedTypes = syncedTypes; }
        
        public java.time.LocalDateTime getLastSuccessfulSync() { return lastSuccessfulSync; }
        public void setLastSuccessfulSync(java.time.LocalDateTime lastSuccessfulSync) { this.lastSuccessfulSync = lastSuccessfulSync; }
        
        public Long getLastSyncDuration() { return lastSyncDuration; }
        public void setLastSyncDuration(Long lastSyncDuration) { this.lastSyncDuration = lastSyncDuration; }
    }
}
