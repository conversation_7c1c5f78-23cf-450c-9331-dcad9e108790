package com.plm.coding.controller;

import com.plm.coding.model.CodingRule;
import com.plm.coding.model.context.CodeGenerationContext;
import com.plm.coding.service.CodeGenerationService;
import com.plm.coding.service.CodingRuleService;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

/**
 * 编码规则控制器
 */
@Tag(name = "编码规则管理")
@Slf4j
@RestController
@RequestMapping("/api/rules")
public class CodeRuleController {
    private final CodingRuleService ruleService;
    private final CodeGenerationService generationService;
    
    @Autowired
    public CodeRuleController(
        CodingRuleService ruleService,
        CodeGenerationService generationService
    ) {
        this.ruleService = ruleService;
        this.generationService = generationService;
    }
    
    @GetMapping
    public ResponseEntity<List<CodingRule>> listRules() {
        return ResponseEntity.ok(ruleService.findAll());
    }

    @GetMapping("/{id}")
    public ResponseEntity<CodingRule> getRule(@PathVariable Long id) {
        return ResponseEntity.ok(ruleService.findById(id));
    }

    @PostMapping
    public ResponseEntity<CodingRule> createRule(@Valid @RequestBody CodingRule rule) {
        return ResponseEntity.ok(ruleService.create(rule));
    }

    @PutMapping("/{id}")
    public ResponseEntity<CodingRule> updateRule(@PathVariable Long id, @Valid @RequestBody CodingRule rule) {
        rule.setId(id);
        return ResponseEntity.ok(ruleService.update(rule));
    }

    @DeleteMapping("/{id}")
    public ResponseEntity<Void> deleteRule(@PathVariable Long id) {
        ruleService.delete(id);
        return ResponseEntity.ok().build();
    }

    @PostMapping("/{id}/generate")
    public ResponseEntity<String> generateCode(@PathVariable Long id, @RequestBody CodeGenerationContext context) {
        CodingRule rule = ruleService.findById(id);
        String code = generationService.generateCode(rule, context);
        return ResponseEntity.ok(code);
    }

    @PostMapping("/{id}/validate")
    public ResponseEntity<Boolean> validateCode(@PathVariable Long id, @RequestBody String code) {
        CodingRule rule = ruleService.findById(id);
        boolean isValid = generationService.validateCode(rule, code);
        return ResponseEntity.ok(isValid);
    }
} 