package com.plm.coding.controller;

import com.plm.coding.model.CodingRule;
import com.plm.coding.model.context.CodeGenerationContext;
import com.plm.coding.service.CodeGenerationService;
import com.plm.coding.service.CodingRuleService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

/**
 * 编码规则控制器
 */
@Api(tags = "编码规则管理", description = "编码规则的增删改查和代码生成功能")
@Slf4j
@RestController
@RequestMapping("/api/rules")
public class CodeRuleController {
    private final CodingRuleService ruleService;
    private final CodeGenerationService generationService;
    
    @Autowired
    public CodeRuleController(
        CodingRuleService ruleService,
        CodeGenerationService generationService
    ) {
        this.ruleService = ruleService;
        this.generationService = generationService;
    }
    
    @ApiOperation(value = "获取所有编码规则", notes = "返回系统中所有的编码规则列表")
    @ApiResponses({
        @ApiResponse(code = 200, message = "成功获取编码规则列表")
    })
    @GetMapping
    public ResponseEntity<List<CodingRule>> listRules() {
        return ResponseEntity.ok(ruleService.findAll());
    }

    @ApiOperation(value = "根据ID获取编码规则", notes = "根据规则ID获取特定的编码规则详情")
    @ApiResponses({
        @ApiResponse(code = 200, message = "成功获取编码规则"),
        @ApiResponse(code = 404, message = "编码规则不存在")
    })
    @GetMapping("/{id}")
    public ResponseEntity<CodingRule> getRule(
        @ApiParam(value = "编码规则ID", required = true) @PathVariable Long id
    ) {
        return ResponseEntity.ok(ruleService.findById(id));
    }

    @ApiOperation(value = "创建新的编码规则", notes = "创建一个新的编码规则")
    @ApiResponses({
        @ApiResponse(code = 200, message = "成功创建编码规则"),
        @ApiResponse(code = 400, message = "请求参数错误")
    })
    @PostMapping
    public ResponseEntity<CodingRule> createRule(
        @ApiParam(value = "编码规则信息", required = true) @Valid @RequestBody CodingRule rule
    ) {
        return ResponseEntity.ok(ruleService.create(rule));
    }
    
    @ApiOperation(value = "更新编码规则", notes = "根据ID更新指定的编码规则")
    @ApiResponses({
        @ApiResponse(code = 200, message = "成功更新编码规则"),
        @ApiResponse(code = 404, message = "编码规则不存在"),
        @ApiResponse(code = 400, message = "请求参数错误")
    })
    @PutMapping("/{id}")
    public ResponseEntity<CodingRule> updateRule(
        @ApiParam(value = "编码规则ID", required = true) @PathVariable Long id,
        @ApiParam(value = "编码规则信息", required = true) @Valid @RequestBody CodingRule rule
    ) {
        rule.setId(id);
        return ResponseEntity.ok(ruleService.update(rule));
    }

    @ApiOperation(value = "删除编码规则", notes = "根据ID删除指定的编码规则")
    @ApiResponses({
        @ApiResponse(code = 200, message = "成功删除编码规则"),
        @ApiResponse(code = 404, message = "编码规则不存在")
    })
    @DeleteMapping("/{id}")
    public ResponseEntity<Void> deleteRule(
        @ApiParam(value = "编码规则ID", required = true) @PathVariable Long id
    ) {
        ruleService.delete(id);
        return ResponseEntity.ok().build();
    }
    
    @ApiOperation(value = "生成编码", notes = "根据编码规则和上下文生成物料编码")
    @ApiResponses({
        @ApiResponse(code = 200, message = "成功生成编码"),
        @ApiResponse(code = 404, message = "编码规则不存在"),
        @ApiResponse(code = 400, message = "生成上下文参数错误")
    })
    @PostMapping("/{id}/generate")
    public ResponseEntity<String> generateCode(
        @ApiParam(value = "编码规则ID", required = true) @PathVariable Long id,
        @ApiParam(value = "代码生成上下文", required = true) @RequestBody CodeGenerationContext context
    ) {
        CodingRule rule = ruleService.findById(id);
        String code = generationService.generateCode(rule, context);
        return ResponseEntity.ok(code);
    }

    @ApiOperation(value = "验证编码", notes = "根据编码规则验证给定的编码是否有效")
    @ApiResponses({
        @ApiResponse(code = 200, message = "验证完成"),
        @ApiResponse(code = 404, message = "编码规则不存在")
    })
    @PostMapping("/{id}/validate")
    public ResponseEntity<Boolean> validateCode(
        @ApiParam(value = "编码规则ID", required = true) @PathVariable Long id,
        @ApiParam(value = "待验证的编码", required = true) @RequestBody String code
    ) {
        CodingRule rule = ruleService.findById(id);
        boolean isValid = generationService.validateCode(rule, code);
        return ResponseEntity.ok(isValid);
    }
} 