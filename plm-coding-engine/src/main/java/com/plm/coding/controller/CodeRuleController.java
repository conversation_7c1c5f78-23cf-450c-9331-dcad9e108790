package com.plm.coding.controller;

import com.plm.coding.model.CodingRule;
import com.plm.coding.model.context.CodeGenerationContext;
import com.plm.coding.service.CodeGenerationService;
import com.plm.coding.service.CodingRuleService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

/**
 * 编码规则控制器
 */
@Tag(name = "编码规则管理", description = "编码规则的增删改查和代码生成功能")
@Slf4j
@RestController
@RequestMapping("/api/rules")
public class CodeRuleController {
    private final CodingRuleService ruleService;
    private final CodeGenerationService generationService;
    
    @Autowired
    public CodeRuleController(
        CodingRuleService ruleService,
        CodeGenerationService generationService
    ) {
        this.ruleService = ruleService;
        this.generationService = generationService;
    }
    
    @Operation(summary = "获取所有编码规则", description = "返回系统中所有的编码规则列表")
    @ApiResponses({
        @ApiResponse(responseCode = "200", description = "成功获取编码规则列表")
    })
    @GetMapping
    public ResponseEntity<List<CodingRule>> listRules() {
        return ResponseEntity.ok(ruleService.findAll());
    }

    @Operation(summary = "根据ID获取编码规则", description = "根据规则ID获取特定的编码规则详情")
    @ApiResponses({
        @ApiResponse(responseCode = "200", description = "成功获取编码规则"),
        @ApiResponse(responseCode = "404", description = "编码规则不存在")
    })
    @GetMapping("/{id}")
    public ResponseEntity<CodingRule> getRule(
        @Parameter(description = "编码规则ID", required = true) @PathVariable Long id
    ) {
        return ResponseEntity.ok(ruleService.findById(id));
    }

    @Operation(summary = "创建新的编码规则", description = "创建一个新的编码规则")
    @ApiResponses({
        @ApiResponse(responseCode = "200", description = "成功创建编码规则"),
        @ApiResponse(responseCode = "400", description = "请求参数错误")
    })
    @PostMapping
    public ResponseEntity<CodingRule> createRule(
        @Parameter(description = "编码规则信息", required = true) @Valid @RequestBody CodingRule rule
    ) {
        return ResponseEntity.ok(ruleService.create(rule));
    }

    @Operation(summary = "更新编码规则", description = "根据ID更新指定的编码规则")
    @ApiResponses({
        @ApiResponse(responseCode = "200", description = "成功更新编码规则"),
        @ApiResponse(responseCode = "404", description = "编码规则不存在"),
        @ApiResponse(responseCode = "400", description = "请求参数错误")
    })
    @PutMapping("/{id}")
    public ResponseEntity<CodingRule> updateRule(
        @Parameter(description = "编码规则ID", required = true) @PathVariable Long id,
        @Parameter(description = "编码规则信息", required = true) @Valid @RequestBody CodingRule rule
    ) {
        rule.setId(id);
        return ResponseEntity.ok(ruleService.update(rule));
    }

    @Operation(summary = "删除编码规则", description = "根据ID删除指定的编码规则")
    @ApiResponses({
        @ApiResponse(responseCode = "200", description = "成功删除编码规则"),
        @ApiResponse(responseCode = "404", description = "编码规则不存在")
    })
    @DeleteMapping("/{id}")
    public ResponseEntity<Void> deleteRule(
        @Parameter(description = "编码规则ID", required = true) @PathVariable Long id
    ) {
        ruleService.delete(id);
        return ResponseEntity.ok().build();
    }

    @Operation(summary = "生成编码", description = "根据编码规则和上下文生成物料编码")
    @ApiResponses({
        @ApiResponse(responseCode = "200", description = "成功生成编码"),
        @ApiResponse(responseCode = "404", description = "编码规则不存在"),
        @ApiResponse(responseCode = "400", description = "生成上下文参数错误")
    })
    @PostMapping("/{id}/generate")
    public ResponseEntity<String> generateCode(
        @Parameter(description = "编码规则ID", required = true) @PathVariable Long id,
        @Parameter(description = "代码生成上下文", required = true) @RequestBody CodeGenerationContext context
    ) {
        CodingRule rule = ruleService.findById(id);
        String code = generationService.generateCode(rule, context);
        return ResponseEntity.ok(code);
    }

    @Operation(summary = "验证编码", description = "根据编码规则验证给定的编码是否有效")
    @ApiResponses({
        @ApiResponse(responseCode = "200", description = "验证完成"),
        @ApiResponse(responseCode = "404", description = "编码规则不存在")
    })
    @PostMapping("/{id}/validate")
    public ResponseEntity<Boolean> validateCode(
        @Parameter(description = "编码规则ID", required = true) @PathVariable Long id,
        @Parameter(description = "待验证的编码", required = true) @RequestBody String code
    ) {
        CodingRule rule = ruleService.findById(id);
        boolean isValid = generationService.validateCode(rule, code);
        return ResponseEntity.ok(isValid);
    }
} 