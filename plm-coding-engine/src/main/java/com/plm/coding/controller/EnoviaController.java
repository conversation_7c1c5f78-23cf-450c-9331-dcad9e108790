package com.plm.coding.controller;

import com.plm.coding.integration.EnoviaConnectionService;
import io.swagger.v3.oas.annotations.tags.Tag;
import matrix.db.Context;
import matrix.util.MatrixException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/api/enovia")
@Tag(name = "Enovia集成")
public class EnoviaController {

    @Autowired
    private EnoviaConnectionService enoviaConnectionService;

    @GetMapping("/status")
    public String getConnectionStatus() {
        try {
            Context context = enoviaConnectionService.getContext();
            return "Connected to Enovia as user: " + context.getUser();
        } catch (Exception e) {
            return "Not connected: " + e.getMessage();
        }
    }

    @GetMapping("/execute-mql")
    public String executeMql(@RequestParam String command) {
        try {
            Context context = enoviaConnectionService.getContext();
            matrix.db.MQLCommand mql = new matrix.db.MQLCommand();
            mql.executeCommand(context, command);
            return mql.getResult();
        } catch (MatrixException e) {
            return "Error: " + e.getMessage();
        }
    }

    @GetMapping("/print-context")
    public String printContext() {
        try {
            Context context = enoviaConnectionService.getContext();
            matrix.db.MQLCommand mql = new matrix.db.MQLCommand();
            mql.executeCommand(context, "print cont");
            return mql.getResult();
        } catch (MatrixException e) {
            return "Error: " + e.getMessage();
        }
    }

}