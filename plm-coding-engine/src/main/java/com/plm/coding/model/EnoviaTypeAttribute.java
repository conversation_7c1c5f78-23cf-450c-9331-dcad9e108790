package com.plm.coding.model;

import lombok.Data;
import lombok.NoArgsConstructor;
import javax.persistence.*;
import java.time.LocalDateTime;

/**
 * Enovia Type 属性模型
 * 对应 Enovia 系统中 Type 的属性定义
 */
@Entity
@Table(name = "ENOVIA_TYPE_ATTRIBUTE", 
       uniqueConstraints = @UniqueConstraint(columnNames = {"TYPE_ID", "ATTRIBUTE_NAME"}))
@Data
@NoArgsConstructor
public class EnoviaTypeAttribute {
    
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    
    /**
     * 关联的 Type
     */
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "TYPE_ID", nullable = false)
    private EnoviaType enoviaType;
    
    /**
     * 属性名称
     */
    @Column(name = "ATTRIBUTE_NAME", nullable = false)
    private String attributeName;
    
    /**
     * 属性显示名称
     */
    @Column(name = "DISPLAY_NAME")
    private String displayName;
    
    /**
     * 属性类型
     */
    @Column(name = "ATTRIBUTE_TYPE", nullable = false)
    private String attributeType;
    
    /**
     * 数据类型
     */
    @Column(name = "DATA_TYPE")
    private String dataType;
    
    /**
     * 最大长度
     */
    @Column(name = "MAX_LENGTH")
    private Integer maxLength;
    
    /**
     * 是否必填
     */
    @Column(name = "IS_REQUIRED")
    private Boolean isRequired = false;
    
    /**
     * 是否多值
     */
    @Column(name = "IS_MULTIVALUE")
    private Boolean isMultivalue = false;
    
    /**
     * 是否隐藏
     */
    @Column(name = "IS_HIDDEN")
    private Boolean isHidden = false;
    
    /**
     * 默认值
     */
    @Column(name = "DEFAULT_VALUE")
    private String defaultValue;
    
    /**
     * 值范围（JSON格式）
     */
    @Column(name = "VALUE_RANGE", columnDefinition = "TEXT")
    private String valueRange;
    
    /**
     * 描述
     */
    @Column(name = "DESCRIPTION", columnDefinition = "TEXT")
    private String description;
    
    /**
     * 同步状态
     */
    @Column(name = "SYNC_STATUS")
    @Enumerated(EnumType.STRING)
    private SyncStatus syncStatus = SyncStatus.PENDING;
    
    /**
     * 最后同步时间
     */
    @Column(name = "LAST_SYNC_TIME")
    private LocalDateTime lastSyncTime;
    
    /**
     * 创建时间
     */
    @Column(name = "CREATED_AT", updatable = false)
    private LocalDateTime createdAt;
    
    /**
     * 更新时间
     */
    @Column(name = "UPDATED_AT")
    private LocalDateTime updatedAt;
    
    @PrePersist
    protected void onCreate() {
        createdAt = LocalDateTime.now();
        updatedAt = LocalDateTime.now();
    }
    
    @PreUpdate
    protected void onUpdate() {
        updatedAt = LocalDateTime.now();
    }
    
    /**
     * 同步状态枚举
     */
    public enum SyncStatus {
        PENDING,    // 待同步
        SYNCING,    // 同步中
        SUCCESS,    // 同步成功
        FAILED      // 同步失败
    }
}
