package com.plm.coding.model;

import lombok.Data;
import lombok.NoArgsConstructor;
import javax.persistence.*;
import java.time.LocalDateTime;

/**
 * Enovia 同步记录模型
 * 记录每次同步的详细信息
 */
@Entity
@Table(name = "ENOVIA_SYNC_RECORD")
@Data
@NoArgsConstructor
public class EnoviaSyncRecord {
    
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    
    /**
     * 同步类型
     */
    @Column(name = "SYNC_TYPE", nullable = false)
    @Enumerated(EnumType.STRING)
    private SyncType syncType;
    
    /**
     * 同步策略
     */
    @Column(name = "SYNC_STRATEGY", nullable = false)
    @Enumerated(EnumType.STRING)
    private SyncStrategy syncStrategy;
    
    /**
     * 同步状态
     */
    @Column(name = "SYNC_STATUS", nullable = false)
    @Enumerated(EnumType.STRING)
    private SyncStatus syncStatus;
    
    /**
     * 开始时间
     */
    @Column(name = "START_TIME", nullable = false)
    private LocalDateTime startTime;
    
    /**
     * 结束时间
     */
    @Column(name = "END_TIME")
    private LocalDateTime endTime;
    
    /**
     * 总记录数
     */
    @Column(name = "TOTAL_RECORDS")
    private Integer totalRecords = 0;
    
    /**
     * 成功记录数
     */
    @Column(name = "SUCCESS_RECORDS")
    private Integer successRecords = 0;
    
    /**
     * 失败记录数
     */
    @Column(name = "FAILED_RECORDS")
    private Integer failedRecords = 0;
    
    /**
     * 跳过记录数
     */
    @Column(name = "SKIPPED_RECORDS")
    private Integer skippedRecords = 0;
    
    /**
     * 错误信息
     */
    @Column(name = "ERROR_MESSAGE", columnDefinition = "TEXT")
    private String errorMessage;
    
    /**
     * 同步详情（JSON格式）
     */
    @Column(name = "SYNC_DETAILS", columnDefinition = "TEXT")
    private String syncDetails;
    
    /**
     * 创建时间
     */
    @Column(name = "CREATED_AT", updatable = false)
    private LocalDateTime createdAt;
    
    @PrePersist
    protected void onCreate() {
        createdAt = LocalDateTime.now();
        if (startTime == null) {
            startTime = LocalDateTime.now();
        }
    }
    
    /**
     * 同步类型枚举
     */
    public enum SyncType {
        TYPES,              // 类型同步
        CLASSIFICATIONS,    // 分类同步
        ATTRIBUTES,         // 属性同步
        ALL                 // 全部同步
    }
    
    /**
     * 同步策略枚举
     */
    public enum SyncStrategy {
        FULL,           // 全量同步
        INCREMENTAL     // 增量同步
    }
    
    /**
     * 同步状态枚举
     */
    public enum SyncStatus {
        PENDING,        // 待执行
        RUNNING,        // 执行中
        SUCCESS,        // 成功
        FAILED,         // 失败
        CANCELLED       // 已取消
    }
}
