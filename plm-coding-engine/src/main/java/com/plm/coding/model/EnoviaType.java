package com.plm.coding.model;

import lombok.Data;
import lombok.NoArgsConstructor;
import javax.persistence.*;
import java.time.LocalDateTime;
import java.util.List;

/**
 * Enovia Type 模型
 * 对应 Enovia 系统中的 Type 定义
 */
@Entity
@Table(name = "ENOVIA_TYPE")
@Data
@NoArgsConstructor
public class EnoviaType {
    
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    
    /**
     * Type 名称
     */
    @Column(name = "TYPE_NAME", nullable = false, unique = true)
    private String typeName;
    
    /**
     * Type 显示名称
     */
    @Column(name = "DISPLAY_NAME")
    private String displayName;
    
    /**
     * 父类型名称
     */
    @Column(name = "PARENT_TYPE")
    private String parentType;
    
    /**
     * 是否为抽象类型
     */
    @Column(name = "IS_ABSTRACT")
    private Boolean isAbstract = false;
    
    /**
     * 是否为隐藏类型
     */
    @Column(name = "IS_HIDDEN")
    private Boolean isHidden = false;
    
    /**
     * 描述
     */
    @Column(name = "DESCRIPTION", columnDefinition = "TEXT")
    private String description;
    
    /**
     * 图标名称
     */
    @Column(name = "ICON_NAME")
    private String iconName;
    
    /**
     * 同步状态
     */
    @Column(name = "SYNC_STATUS")
    @Enumerated(EnumType.STRING)
    private SyncStatus syncStatus = SyncStatus.PENDING;
    
    /**
     * 最后同步时间
     */
    @Column(name = "LAST_SYNC_TIME")
    private LocalDateTime lastSyncTime;
    
    /**
     * 创建时间
     */
    @Column(name = "CREATED_AT", updatable = false)
    private LocalDateTime createdAt;
    
    /**
     * 更新时间
     */
    @Column(name = "UPDATED_AT")
    private LocalDateTime updatedAt;
    
    /**
     * Type 的属性定义
     */
    @OneToMany(mappedBy = "enoviaType", cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    private List<EnoviaTypeAttribute> attributes;
    
    @PrePersist
    protected void onCreate() {
        createdAt = LocalDateTime.now();
        updatedAt = LocalDateTime.now();
    }
    
    @PreUpdate
    protected void onUpdate() {
        updatedAt = LocalDateTime.now();
    }
    
    /**
     * 同步状态枚举
     */
    public enum SyncStatus {
        PENDING,    // 待同步
        SYNCING,    // 同步中
        SUCCESS,    // 同步成功
        FAILED      // 同步失败
    }
}
