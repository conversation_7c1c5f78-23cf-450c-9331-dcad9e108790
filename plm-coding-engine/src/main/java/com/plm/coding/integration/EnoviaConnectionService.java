package com.plm.coding.integration;

import matrix.db.Context;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import javax.annotation.PreDestroy;
import javax.net.ssl.HttpsURLConnection;
import javax.net.ssl.SSLContext;
import javax.net.ssl.TrustManager;
import javax.net.ssl.X509TrustManager;
import java.security.cert.X509Certificate;

@Service
public class EnoviaConnectionService {
    private static final Logger logger = LoggerFactory.getLogger(EnoviaConnectionService.class);

    @Autowired
    private EnoviaProperties enoviaProperties;

    private Context context;
    private boolean isLocalEnvironment;
    private String hostname;

    @PostConstruct
    public void initialize() {
        try {
            connect();
            logger.info("Successfully connected to Enovia server: {}", hostname);
        } catch (Exception e) {
            logger.error("Failed to connect to Enovia server", e);
            throw new RuntimeException("Failed to connect to Enovia server", e);
        }
    }

    @PreDestroy
    public void cleanup() {
        if (context != null) {
            try {
                context.disconnect();
                logger.info("Disconnected from Enovia server");
            } catch (Exception e) {
                logger.error("Error disconnecting from Enovia server", e);
            }
        }
    }

    public Context getContext() {
        if (context == null || !context.isConnected()) {
            throw new RuntimeException("Not connected to Enovia server");
        }
        return context;
    }

    private void connect() throws Exception {
        setTrustManager(enoviaProperties.isUseCertificates());

        if (enoviaProperties.getHost().startsWith("http")) {
            isLocalEnvironment = false;
            if (enoviaProperties.isUse3DPassport()) {
                context = new Context(enoviaProperties.getHost() +
                        getTicket(
                                enoviaProperties.getHost(),
                                enoviaProperties.getUser(),
                                enoviaProperties.getPassword()
                        ));
            } else {
                context = new Context(enoviaProperties.getHost());
                context.setUser(enoviaProperties.getUser());
                context.setPassword(enoviaProperties.getPassword());
            }
        } else {
            isLocalEnvironment = true;
            context = new Context(enoviaProperties.getHost());
            context.setPassword(enoviaProperties.getPassword());
        }

        context.setUser(enoviaProperties.getUser());
        context.setRole(enoviaProperties.getRole());
        context.setVault(enoviaProperties.getVault());
        context.connect();

        // 设置主机名
        if (enoviaProperties.getHost().isEmpty()) {
            hostname = "localhost";
        } else {
            if (isLocalEnvironment) {
                hostname = enoviaProperties.getHost();
            } else {
                hostname = enoviaProperties.getHost().substring(enoviaProperties.getHost().indexOf("//") + 2);
                hostname = hostname.substring(0, hostname.indexOf("/"));
                if (hostname.contains(":")) {
                    hostname = hostname.substring(0, hostname.indexOf(":"));
                }
            }
        }
    }

    private void setTrustManager(boolean useCertificates) throws Exception {
        SSLContext sc = SSLContext.getInstance("TLS");
        if (useCertificates) {
            sc.init(null, null, null);
        } else {
            sc.init(null, new TrustManager[]{new TrustAllTrustManager()}, null);
        }

        HttpsURLConnection.setDefaultSSLSocketFactory(sc.getSocketFactory());
    }

    private String getTicket(String host, String user, String password) throws Exception {
        // 这里调用Passport类的getTicket方法
        // 为简化，我们将该方法的核心逻辑复制到这里
        // 实际项目中应考虑将Passport类整体迁移过来

        // 简化版的实现，实际项目中需要完整实现
        return EnoviaPassportService.getTicket(host, user, password);
    }

    public static class TrustAllTrustManager implements X509TrustManager {
        public X509Certificate[] getAcceptedIssuers() {
            return new X509Certificate[0];
        }

        public void checkClientTrusted(X509Certificate[] chain, String authType) {
        }

        public void checkServerTrusted(X509Certificate[] chain, String authType) {
        }
    }
}