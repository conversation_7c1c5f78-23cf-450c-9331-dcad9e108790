package com.plm.coding.integration;

import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
@EnableConfigurationProperties(EnoviaProperties.class)
@ConditionalOnProperty(prefix = "enovia", name = "enabled", havingValue = "true")
public class EnoviaAutoConfiguration {

    @Bean
    @ConditionalOnMissingBean
    public EnoviaPassportService enoviaPassportService() {
        return new EnoviaPassportService();
    }

    @Bean
    @ConditionalOnMissingBean
    public EnoviaConnectionService enoviaConnectionService() {
        return new EnoviaConnectionService();
    }
}