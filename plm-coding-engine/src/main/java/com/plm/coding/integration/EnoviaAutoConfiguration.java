package com.plm.coding.integration;

import com.plm.coding.integration.sync.EnoviaSyncProperties;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.annotation.EnableScheduling;

@Configuration
@EnableConfigurationProperties({EnoviaProperties.class, EnoviaSyncProperties.class})
@EnableScheduling
@ConditionalOnProperty(prefix = "enovia", name = "enabled", havingValue = "true")
public class EnoviaAutoConfiguration {

    @Bean
    @ConditionalOnMissingBean
    public EnoviaPassportService enoviaPassportService() {
        return new EnoviaPassportService();
    }

    @Bean
    @ConditionalOnMissingBean
    public EnoviaConnectionService enoviaConnectionService() {
        return new EnoviaConnectionService();
    }


}