package com.plm.coding.integration.sync;

import com.plm.coding.model.EnoviaSyncRecord;
import com.plm.coding.repository.EnoviaSyncRecordRepository;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.util.List;

/**
 * Enovia 数据同步定时任务调度器
 * 根据配置自动执行数据同步任务
 */
@Slf4j
@Service
@ConditionalOnProperty(prefix = "enovia.sync", name = "enabled", havingValue = "true")
public class EnoviaSyncScheduler {
    
    @Autowired
    private EnoviaDataSyncService syncService;
    
    @Autowired
    private EnoviaSyncProperties syncProperties;
    
    @Autowired
    private EnoviaSyncRecordRepository syncRecordRepository;
    
    /**
     * 应用启动后自动执行同步（如果配置了自动启动）
     */
    @PostConstruct
    public void initializeSync() {
        if (syncProperties.isAutoStart()) {
            log.info("应用启动，准备执行自动同步");
            try {
                // 检查是否有正在运行的同步任务
                List<EnoviaSyncRecord.SyncStatus> runningStatuses = List.of(
                    EnoviaSyncRecord.SyncStatus.PENDING,
                    EnoviaSyncRecord.SyncStatus.RUNNING
                );
                
                List<EnoviaSyncRecord> runningTasks = syncRecordRepository.findBySyncStatusIn(runningStatuses);
                
                if (runningTasks.isEmpty()) {
                    log.info("没有正在运行的同步任务，启动初始同步");
                    if ("full".equalsIgnoreCase(syncProperties.getStrategy())) {
                        syncService.performFullSync();
                    } else {
                        syncService.performIncrementalSync();
                    }
                } else {
                    log.info("检测到 {} 个正在运行的同步任务，跳过自动启动", runningTasks.size());
                }
            } catch (Exception e) {
                log.error("自动启动同步失败", e);
            }
        }
    }
    
    /**
     * 定时执行增量同步
     * 默认每小时执行一次，可通过配置调整
     */
    @Scheduled(fixedRateString = "#{@enoviaSyncProperties.incrementalInterval.toMillis()}")
    public void scheduleIncrementalSync() {
        if (!syncProperties.isEnabled()) {
            return;
        }
        
        try {
            // 检查是否有正在运行的同步任务
            List<EnoviaSyncRecord.SyncStatus> runningStatuses = List.of(
                EnoviaSyncRecord.SyncStatus.PENDING,
                EnoviaSyncRecord.SyncStatus.RUNNING
            );
            
            List<EnoviaSyncRecord> runningTasks = syncRecordRepository.findBySyncStatusIn(runningStatuses);
            
            if (runningTasks.isEmpty()) {
                log.info("开始执行定时增量同步");
                syncService.performIncrementalSync();
            } else {
                log.debug("检测到正在运行的同步任务，跳过本次增量同步");
            }
        } catch (Exception e) {
            log.error("定时增量同步执行失败", e);
        }
    }
    
    /**
     * 定时执行全量同步
     * 默认每24小时执行一次，可通过配置调整
     */
    @Scheduled(fixedRateString = "#{@enoviaSyncProperties.fullSyncInterval.toMillis()}")
    public void scheduleFullSync() {
        if (!syncProperties.isEnabled()) {
            return;
        }
        
        try {
            // 检查是否有正在运行的同步任务
            List<EnoviaSyncRecord.SyncStatus> runningStatuses = List.of(
                EnoviaSyncRecord.SyncStatus.PENDING,
                EnoviaSyncRecord.SyncStatus.RUNNING
            );
            
            List<EnoviaSyncRecord> runningTasks = syncRecordRepository.findBySyncStatusIn(runningStatuses);
            
            if (runningTasks.isEmpty()) {
                log.info("开始执行定时全量同步");
                syncService.performFullSync();
            } else {
                log.debug("检测到正在运行的同步任务，跳过本次全量同步");
            }
        } catch (Exception e) {
            log.error("定时全量同步执行失败", e);
        }
    }
    
    /**
     * 清理过期的同步记录
     * 每天凌晨2点执行，保留最近30天的记录
     */
    @Scheduled(cron = "0 0 2 * * ?")
    public void cleanupOldSyncRecords() {
        if (!syncProperties.isEnabled()) {
            return;
        }
        
        try {
            log.info("开始清理过期的同步记录");
            
            // 删除30天前的同步记录（保留成功和失败的记录用于统计）
            java.time.LocalDateTime cutoffDate = java.time.LocalDateTime.now().minusDays(30);
            
            List<EnoviaSyncRecord> oldRecords = syncRecordRepository.findByStartTimeBefore(cutoffDate);
            
            if (!oldRecords.isEmpty()) {
                // 只删除成功的记录，保留失败的记录用于问题排查
                List<EnoviaSyncRecord> recordsToDelete = oldRecords.stream()
                    .filter(record -> record.getSyncStatus() == EnoviaSyncRecord.SyncStatus.SUCCESS)
                    .toList();
                
                if (!recordsToDelete.isEmpty()) {
                    syncRecordRepository.deleteAll(recordsToDelete);
                    log.info("已清理 {} 条过期的同步记录", recordsToDelete.size());
                }
            } else {
                log.debug("没有找到需要清理的过期同步记录");
            }
        } catch (Exception e) {
            log.error("清理过期同步记录失败", e);
        }
    }
    
    /**
     * 监控同步任务状态
     * 每5分钟检查一次，处理超时的同步任务
     */
    @Scheduled(fixedRate = 300000) // 5分钟
    public void monitorSyncTasks() {
        if (!syncProperties.isEnabled()) {
            return;
        }
        
        try {
            // 查找运行时间超过2小时的同步任务，标记为失败
            java.time.LocalDateTime timeoutThreshold = java.time.LocalDateTime.now().minusHours(2);
            
            List<EnoviaSyncRecord> timeoutTasks = syncRecordRepository.findBySyncStatusAndStartTimeBefore(
                EnoviaSyncRecord.SyncStatus.RUNNING, timeoutThreshold);
            
            for (EnoviaSyncRecord task : timeoutTasks) {
                log.warn("检测到超时的同步任务，任务ID: {}, 开始时间: {}", task.getId(), task.getStartTime());
                task.setSyncStatus(EnoviaSyncRecord.SyncStatus.FAILED);
                task.setErrorMessage("同步任务超时（超过2小时）");
                task.setEndTime(java.time.LocalDateTime.now());
                syncRecordRepository.save(task);
            }
            
            if (!timeoutTasks.isEmpty()) {
                log.info("已处理 {} 个超时的同步任务", timeoutTasks.size());
            }
        } catch (Exception e) {
            log.error("监控同步任务状态失败", e);
        }
    }
}
