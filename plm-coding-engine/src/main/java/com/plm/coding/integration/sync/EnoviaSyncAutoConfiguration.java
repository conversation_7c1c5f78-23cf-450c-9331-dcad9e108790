package com.plm.coding.integration.sync;

import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.annotation.EnableScheduling;

/**
 * Enovia 数据同步自动配置类
 */
@Configuration
@EnableConfigurationProperties(EnoviaSyncProperties.class)
@EnableScheduling
@ConditionalOnProperty(prefix = "enovia.sync", name = "enabled", havingValue = "true")
public class EnoviaSyncAutoConfiguration {
    // 配置类，Spring Boot 会自动创建 EnoviaSyncProperties bean
}
