package com.plm.coding.integration.sync;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

import java.time.Duration;
import java.util.List;

/**
 * Enovia 数据同步配置属性
 */
@Data
@Component
@ConfigurationProperties(prefix = "enovia.sync")
public class EnoviaSyncProperties {
    
    /**
     * 是否启用同步功能
     */
    private boolean enabled = true;
    
    /**
     * 是否自动启动同步
     */
    private boolean autoStart = false;
    
    /**
     * 批处理大小
     */
    private int batchSize = 100;
    
    /**
     * 重试次数
     */
    private int retryCount = 3;
    
    /**
     * 重试延迟（毫秒）
     */
    private long retryDelay = 5000;
    
    /**
     * 同步策略：incremental（增量）或 full（全量）
     */
    private String strategy = "incremental";
    
    /**
     * 全量同步间隔
     */
    private Duration fullSyncInterval = Duration.ofHours(24);
    
    /**
     * 增量同步间隔
     */
    private Duration incrementalInterval = Duration.ofHours(1);
    
    /**
     * 类型同步配置
     */
    private TypeSyncConfig types = new TypeSyncConfig();
    
    /**
     * 分类同步配置
     */
    private ClassificationSyncConfig classifications = new ClassificationSyncConfig();
    
    /**
     * 属性同步配置
     */
    private AttributeSyncConfig attributes = new AttributeSyncConfig();
    
    @Data
    public static class TypeSyncConfig {
        private boolean enabled = true;
        private List<String> include;
        private List<String> exclude;
    }
    
    @Data
    public static class ClassificationSyncConfig {
        private boolean enabled = true;
        private List<String> include;
        private List<String> exclude;
    }
    
    @Data
    public static class AttributeSyncConfig {
        private boolean enabled = true;
        private List<String> include;
        private List<String> exclude;
    }
}
