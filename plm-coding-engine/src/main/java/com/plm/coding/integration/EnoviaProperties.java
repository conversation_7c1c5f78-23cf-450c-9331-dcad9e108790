package com.plm.coding.integration;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

@Data
@Component
@ConfigurationProperties(prefix = "enovia")
public class EnoviaProperties {
    private String host;
    private String user;
    private String password;
    private String vault;
    private String role = "";
    private boolean use3DPassport = true;
    private boolean useCertificates = false;
}