package com.plm.coding.integration;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.w3c.dom.Document;
import org.xml.sax.InputSource;
import org.xml.sax.SAXException;

import javax.xml.parsers.DocumentBuilder;
import javax.xml.parsers.DocumentBuilderFactory;
import javax.xml.parsers.ParserConfigurationException;
import java.io.*;
import java.net.HttpURLConnection;
import java.net.URL;
import java.net.URLConnection;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Properties;

@Service
public class EnoviaPassportService {
    private static final Logger logger = LoggerFactory.getLogger(EnoviaPassportService.class);
    private static final ObjectMapper objectMapper = new ObjectMapper();
    static final String kernelServlet = "/servlet/MatrixXMLServlet";

    public static String getTicket(String host, String user, String password) throws Exception {
        HttpURLConnection conTemp = getHttpConnection(host + kernelServlet);
        int respTemp = conTemp.getResponseCode();

        if (respTemp != 302) {
            if (respTemp == 400) {
                throw new Exception("Error 400: Bad request");
            } else if (respTemp == 404) {
                throw new Exception("Error 404: Not found");
            } else {
                throw new Exception("Required CAS redirect not found");
            }
        } else {
            String redirectUrl = conTemp.getHeaderField("Location");
            logger.debug("redirectUrl: {}", redirectUrl);

            HttpURLConnection conCAS = getHttpConnection(redirectUrl);
            Map<String, String> cookiesCAS = getCASCookies(conCAS);

            StringBuilder cookies = new StringBuilder();
            String jSessionId = cookiesCAS.get("JSESSIONID");
            if (jSessionId != null) {
                cookies.append("JSESSIONID=").append(jSessionId).append(";");
            }

            String serverId = cookiesCAS.get("SERVERID");
            if (serverId != null) {
                cookies.append("SERVERID=").append(serverId).append(";");
            }

            logger.debug("COOKIES: {}", cookies);

            String tenant = null;
            if (serverId != null && host.endsWith("enovia")) {
                tenant = host.substring(host.indexOf("//") + 2, host.indexOf("-")).toUpperCase();
            }

            String authParamsCAS = getAuthParams(conCAS);
            logger.debug("authParamsCAS: {}", authParamsCAS);

            try {
                JsonNode jsonCAS = objectMapper.readTree(authParamsCAS);
                String lt = jsonCAS.get("lt").asText();
                logger.debug("lt: {}", lt);

                String loginUrlCAS = jsonCAS.get("url").asText();
                logger.debug("loginUrlCAS: {}", loginUrlCAS);

                HttpURLConnection conCASLogin = getHttpConnection(loginUrlCAS);
                conCASLogin.setRequestProperty("Cookie", cookies.toString());
                conCASLogin.setRequestMethod("POST");
                conCASLogin.setDoOutput(true);

                Properties casUrlParamProperties = new Properties();
                casUrlParamProperties.put("lt", lt);
                casUrlParamProperties.put("username", user);
                casUrlParamProperties.put("password", password);

                String casUrlParams = encodeUrlParams(casUrlParamProperties);

                try (DataOutputStream wr = new DataOutputStream(conCASLogin.getOutputStream())) {
                    wr.writeBytes(casUrlParams);
                    wr.flush();
                }

                int respCodeCASLogin = conCASLogin.getResponseCode();
                logger.debug("respCodeCASLogin: {}", respCodeCASLogin);

                if (respCodeCASLogin == 302) {
                    String redirectUrlFromCASLogin = conCASLogin.getHeaderField("Location");
                    logger.debug("redirectUrlFromCASLogin: {}", redirectUrlFromCASLogin);

                    if (redirectUrlFromCASLogin.contains("?ticket=")) {
                        String ticket =
                                redirectUrlFromCASLogin.substring(redirectUrlFromCASLogin.lastIndexOf("?ticket="));
                        if (tenant != null) {
                            ticket = ticket + "&tenant=" + tenant;
                        }

                        logger.debug("ticket: {}", ticket);
                        return ticket;
                    } else {
                        throw new Exception("Required CAS Ticket not found");
                    }
                } else {
                    String redirectUrlFromCASLogin = getAuthParams(conCASLogin);
                    String errorMsg = getErrorMsg(redirectUrlFromCASLogin);

                    logger.debug("errorMsg: {}", errorMsg);
                    throw new Exception(errorMsg);
                }
            } catch (IOException e) {
                logger.error("Error parsing JSON response: {}", e.getMessage());
                throw new Exception("Failed to parse authentication parameters", e);
            }
        }
    }

    private static String getErrorMsg(String redirectUrlFromCASLogin) {
        try {
            JsonNode jsonCASLogin = objectMapper.readTree(redirectUrlFromCASLogin);
            JsonNode jsonArrayCASLogin = jsonCASLogin.get("errorMsgs");
            StringBuilder errorMsg = new StringBuilder();

            if (jsonArrayCASLogin != null && jsonArrayCASLogin.isArray()) {
                for (JsonNode errorNode : jsonArrayCASLogin) {
                    JsonNode defaultMessage = errorNode.get("defaultMessage");
                    if (defaultMessage != null) {
                        errorMsg.append(defaultMessage.asText());
                    }
                }
            }

            if (errorMsg.length() == 0) {
                return "Internal Server Error";
            }

            return errorMsg.toString();
        } catch (Exception e) {
            logger.error("Error parsing JSON response: {}", e.getMessage());
            return "Internal Server Error";
        }
    }

    private static HttpURLConnection getHttpConnection(String sUrl) throws IOException {
        URL url = new URL(sUrl);
        HttpURLConnection con = (HttpURLConnection) url.openConnection();
        con.setInstanceFollowRedirects(false);
        return con;
    }

    private static Map<String, String> getCASCookies(URLConnection con) {
        Map<String, String> cookies = new HashMap<>();
        Map<String, List<String>> headers = con.getHeaderFields();

        for (Map.Entry<String, List<String>> entry : headers.entrySet()) {
            String headerKey = entry.getKey();

            if (headerKey != null && headerKey.equalsIgnoreCase("Set-Cookie")) {
                for (String headerValue : entry.getValue()) {
                    if (headerValue != null) {
                        String[] fields = headerValue.split(";\\s*");
                        String cookieValue = fields[0];
                        String[] a = cookieValue.split("=", 2);
                        cookies.put(a[0], a[1]);
                    }
                }

                return cookies;
            }
        }

        return cookies;
    }

    private static String getAuthParams(URLConnection con) throws ParserConfigurationException, SAXException, IOException {
        String page = inputStreamToString(con.getInputStream());
        BufferedReader br = new BufferedReader(new StringReader(page));
        String authParams = null;

        String line;
        while ((line = br.readLine()) != null) {
            if (line.contains("id=\"configData\"")) {
                DocumentBuilder db = DocumentBuilderFactory.newInstance().newDocumentBuilder();
                Document doc = db.parse(new InputSource(new StringReader(line)));
                authParams = doc.getDocumentElement().getTextContent();
                break;
            }
        }
        br.close();
        return authParams;
    }

    private static String inputStreamToString(InputStream inputStream) throws IOException {
        StringWriter writer = new StringWriter();
        new InputStreamReader(inputStream, StandardCharsets.UTF_8).transferTo(writer);
        return writer.toString();
    }

    private static String encodeUrlParams(Properties props) throws UnsupportedEncodingException {
        StringBuilder result = new StringBuilder();
        boolean first = true;

        for (Map.Entry<Object, Object> entry : props.entrySet()) {
            if (first) {
                first = false;
            } else {
                result.append("&");
            }

            result.append(URLEncoder.encode(entry.getKey().toString(), StandardCharsets.UTF_8));
            result.append("=");
            result.append(URLEncoder.encode(entry.getValue().toString(), StandardCharsets.UTF_8));
        }

        return result.toString();
    }
}