package com.plm.coding.repository;

import com.plm.coding.model.EnoviaType;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

/**
 * Enovia Type Repository
 */
@Repository
public interface EnoviaTypeRepository extends JpaRepository<EnoviaType, Long> {
    
    /**
     * 根据类型名称查找
     */
    Optional<EnoviaType> findByTypeName(String typeName);
    
    /**
     * 根据父类型查找
     */
    List<EnoviaType> findByParentType(String parentType);
    
    /**
     * 根据同步状态查找
     */
    List<EnoviaType> findBySyncStatus(EnoviaType.SyncStatus syncStatus);

    /**
     * 根据同步状态查找（分页）
     */
    Page<EnoviaType> findBySyncStatus(EnoviaType.SyncStatus syncStatus, Pageable pageable);

    /**
     * 根据同步状态统计数量
     */
    Long countBySyncStatus(EnoviaType.SyncStatus syncStatus);

    /**
     * 查找需要同步的类型（最后同步时间早于指定时间）
     */
    @Query("SELECT t FROM EnoviaType t WHERE t.lastSyncTime IS NULL OR t.lastSyncTime < :syncTime")
    List<EnoviaType> findTypesNeedingSync(LocalDateTime syncTime);

    /**
     * 根据是否抽象类型查找
     */
    List<EnoviaType> findByIsAbstract(Boolean isAbstract);

    /**
     * 根据是否隐藏查找
     */
    List<EnoviaType> findByIsHidden(Boolean isHidden);

    /**
     * 检查类型名称是否存在
     */
    boolean existsByTypeName(String typeName);
}
