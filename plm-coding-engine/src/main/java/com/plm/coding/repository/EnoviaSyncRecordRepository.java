package com.plm.coding.repository;

import com.plm.coding.model.EnoviaSyncRecord;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

/**
 * Enovia Sync Record Repository
 */
@Repository
public interface EnoviaSyncRecordRepository extends JpaRepository<EnoviaSyncRecord, Long> {
    
    /**
     * 根据同步类型查找记录
     */
    List<EnoviaSyncRecord> findBySyncType(EnoviaSyncRecord.SyncType syncType);

    /**
     * 根据同步类型查找记录（分页）
     */
    Page<EnoviaSyncRecord> findBySyncType(EnoviaSyncRecord.SyncType syncType, Pageable pageable);

    /**
     * 根据同步状态查找记录
     */
    List<EnoviaSyncRecord> findBySyncStatus(EnoviaSyncRecord.SyncStatus syncStatus);

    /**
     * 根据同步状态查找记录（分页）
     */
    Page<EnoviaSyncRecord> findBySyncStatus(EnoviaSyncRecord.SyncStatus syncStatus, Pageable pageable);

    /**
     * 根据同步类型和状态查找记录
     */
    List<EnoviaSyncRecord> findBySyncTypeAndSyncStatus(
            EnoviaSyncRecord.SyncType syncType,
            EnoviaSyncRecord.SyncStatus syncStatus);

    /**
     * 根据同步类型和状态查找记录（分页）
     */
    Page<EnoviaSyncRecord> findBySyncTypeAndSyncStatus(
            EnoviaSyncRecord.SyncType syncType,
            EnoviaSyncRecord.SyncStatus syncStatus,
            Pageable pageable);
    
    /**
     * 查找最近的同步记录
     */
    @Query("SELECT r FROM EnoviaSyncRecord r WHERE r.syncType = :syncType ORDER BY r.startTime DESC")
    List<EnoviaSyncRecord> findRecentSyncRecords(EnoviaSyncRecord.SyncType syncType);
    
    /**
     * 查找最后一次成功的同步记录
     */
    @Query("SELECT r FROM EnoviaSyncRecord r WHERE r.syncType = :syncType AND r.syncStatus = 'SUCCESS' ORDER BY r.endTime DESC")
    Optional<EnoviaSyncRecord> findLastSuccessfulSync(EnoviaSyncRecord.SyncType syncType);
    
    /**
     * 查找指定时间范围内的同步记录
     */
    List<EnoviaSyncRecord> findByStartTimeBetween(LocalDateTime startTime, LocalDateTime endTime);
    
    /**
     * 查找正在运行的同步任务
     */
    List<EnoviaSyncRecord> findBySyncStatusIn(List<EnoviaSyncRecord.SyncStatus> statuses);
    
    /**
     * 统计同步记录数量
     */
    @Query("SELECT COUNT(r) FROM EnoviaSyncRecord r WHERE r.syncType = :syncType AND r.syncStatus = :syncStatus")
    Long countBySyncTypeAndStatus(EnoviaSyncRecord.SyncType syncType, EnoviaSyncRecord.SyncStatus syncStatus);

    /**
     * 查找指定时间之前的同步记录
     */
    List<EnoviaSyncRecord> findByStartTimeBefore(LocalDateTime startTime);

    /**
     * 根据同步状态和开始时间查找记录
     */
    List<EnoviaSyncRecord> findBySyncStatusAndStartTimeBefore(EnoviaSyncRecord.SyncStatus syncStatus, LocalDateTime startTime);
}
