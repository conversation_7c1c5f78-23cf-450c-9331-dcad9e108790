package com.plm.coding.service;

import com.plm.coding.model.CodingRule;
import com.plm.coding.repository.CodingRuleRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.persistence.EntityNotFoundException;
import java.util.List;

/**
 * 编码规则服务
 */
@Service
@Transactional
public class CodingRuleService {
    private final CodingRuleRepository ruleRepository;
    
    @Autowired
    public CodingRuleService(CodingRuleRepository ruleRepository) {
        this.ruleRepository = ruleRepository;
    }
    
    @Cacheable(value = "codingRules", key = "'all'")
    public List<CodingRule> findAll() {
        return ruleRepository.findAll();
    }

    @Cacheable(value = "codingRules", key = "#id")
    public CodingRule findById(Long id) {
        return ruleRepository.findById(id)
            .orElseThrow(() -> new EntityNotFoundException(
                "Rule not found with id: " + id
            ));
    }
    
    @CacheEvict(value = "codingRules", allEntries = true)
    public CodingRule create(CodingRule rule) {
        rule.setId(null); // 确保创建新记录
        return ruleRepository.save(rule);
    }

    @CacheEvict(value = "codingRules", key = "#rule.id")
    public CodingRule update(CodingRule rule) {
        if (rule.getId() == null ||
            !ruleRepository.existsById(rule.getId())) {
            throw new EntityNotFoundException(
                "Rule not found with id: " + rule.getId()
            );
        }
        return ruleRepository.save(rule);
    }

    @CacheEvict(value = "codingRules", key = "#id")
    public void delete(Long id) {
        if (!ruleRepository.existsById(id)) {
            throw new EntityNotFoundException(
                "Rule not found with id: " + id
            );
        }
        ruleRepository.deleteById(id);
    }
    
    @Cacheable(value = "codingRules", key = "'objectType:' + #objectType")
    public List<CodingRule> findByObjectType(String objectType) {
        return ruleRepository.findByObjectTypeCodeOrderByPriorityAsc(objectType);
    }
    
    public boolean existsByName(String name) {
        return ruleRepository.existsByRuleName(name);
    }
} 