// SequenceService.java (No change)
package com.plm.coding.service;

import com.plm.coding.model.SequenceConfig;
import com.plm.coding.repository.SequenceConfigRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Optional;

/**
 * Sequence number service.
 * Now uses SequenceConfigRepository for persistent sequence number generation.
 */
@Service
public class SequenceService {

    @Autowired
    private SequenceConfigRepository sequenceConfigRepository;

    /**
     * Gets the next sequence number for the specified sequence name.
     * This method is transactional to ensure atomicity of sequence updates.
     *
     * @param sequenceName The name of the sequence
     * @param length The desired length of the sequence number, padded with leading zeros if shorter
     * @return The formatted next sequence number
     */
    @Transactional
    public String getNextSequence(String sequenceName, int length) {
        Optional<SequenceConfig> optionalConfig = sequenceConfigRepository.findBySequenceName(sequenceName);

        SequenceConfig config;
        if (optionalConfig.isPresent()) {
            config = optionalConfig.get();
            config.setCurrentValue(config.getCurrentValue() + config.getIncrementBy());
        } else {
            // If sequence config not found, create a default one
            config = new SequenceConfig();
            config.setSequenceName(sequenceName);
            config.setCurrentValue(1L); // Start from 1
            config.setPrefix("");
            config.setSuffix("");
            config.setIncrementBy(1);
            System.out.println("Sequence config for " + sequenceName + " not found, creating default.");
        }
        sequenceConfigRepository.save(config); // Persist the updated/new config

        // Format the sequence number, padding with leading zeros if shorter than length
        return String.format("%0" + length + "d", config.getCurrentValue());
    }

    /**
     * 只预览下一个顺序码，不落库，仅用于测试/预览
     * @param sequenceName 序列名
     * @param length 序列长度
     * @return 预览下一个顺序码
     */
    public String peekNextSequence(String sequenceName, int length) {
        Optional<SequenceConfig> optionalConfig = sequenceConfigRepository.findBySequenceName(sequenceName);
        SequenceConfig config;
        long nextValue;
        if (optionalConfig.isPresent()) {
            config = optionalConfig.get();
            nextValue = config.getCurrentValue() + config.getIncrementBy();
        } else {
            // 如果没有配置，预览时也从1开始
            nextValue = 1L;
        }
        return String.format("%0" + length + "d", nextValue);
    }
}
