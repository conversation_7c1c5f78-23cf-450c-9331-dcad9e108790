<template>
  <div class="enovia-sync">
    <div class="page-header">
      <h1>Enovia 数据同步管理</h1>
      <p>管理从 Enovia 系统到本地数据库的数据同步</p>
    </div>

    <!-- 同步控制面板 -->
    <div class="sync-controls">
      <el-card title="同步控制">
        <template #header>
          <span>同步控制</span>
        </template>
        <div class="control-buttons">
          <el-button 
            type="primary" 
            @click="startFullSync"
            :loading="fullSyncLoading"
            icon="el-icon-refresh">
            启动全量同步
          </el-button>
          <el-button 
            type="success" 
            @click="startIncrementalSync"
            :loading="incrementalSyncLoading"
            icon="el-icon-refresh-right">
            启动增量同步
          </el-button>
          <el-button 
            type="warning" 
            @click="clearCache"
            :loading="clearCacheLoading"
            icon="el-icon-delete">
            清除缓存
          </el-button>
        </div>
      </el-card>
    </div>

    <!-- 同步统计 -->
    <div class="sync-statistics">
      <el-card title="同步统计">
        <template #header>
          <span>同步统计</span>
          <el-button 
            style="float: right; padding: 3px 0" 
            type="text" 
            @click="loadStatistics">
            刷新
          </el-button>
        </template>
        <el-row :gutter="20">
          <el-col :span="6">
            <div class="stat-item">
              <div class="stat-value">{{ statistics.totalSyncRecords || 0 }}</div>
              <div class="stat-label">总同步记录</div>
            </div>
          </el-col>
          <el-col :span="6">
            <div class="stat-item">
              <div class="stat-value success">{{ statistics.successfulSyncs || 0 }}</div>
              <div class="stat-label">成功同步</div>
            </div>
          </el-col>
          <el-col :span="6">
            <div class="stat-item">
              <div class="stat-value error">{{ statistics.failedSyncs || 0 }}</div>
              <div class="stat-label">失败同步</div>
            </div>
          </el-col>
          <el-col :span="6">
            <div class="stat-item">
              <div class="stat-value">{{ statistics.totalTypes || 0 }}</div>
              <div class="stat-label">同步类型数</div>
            </div>
          </el-col>
        </el-row>
        <div class="last-sync" v-if="statistics.lastSuccessfulSync">
          <p><strong>最后成功同步:</strong> {{ formatDateTime(statistics.lastSuccessfulSync) }}</p>
          <p><strong>同步耗时:</strong> {{ formatDuration(statistics.lastSyncDuration) }}</p>
        </div>
      </el-card>
    </div>

    <!-- 正在运行的任务 -->
    <div class="running-tasks">
      <el-card title="正在运行的任务">
        <template #header>
          <span>正在运行的任务</span>
          <el-button 
            style="float: right; padding: 3px 0" 
            type="text" 
            @click="loadRunningTasks">
            刷新
          </el-button>
        </template>
        <el-table :data="runningTasks" v-loading="runningTasksLoading">
          <el-table-column prop="id" label="任务ID" width="80"></el-table-column>
          <el-table-column prop="syncType" label="同步类型" width="120"></el-table-column>
          <el-table-column prop="syncStrategy" label="同步策略" width="120"></el-table-column>
          <el-table-column prop="syncStatus" label="状态" width="100">
            <template #default="scope">
              <el-tag :type="getStatusType(scope.row.syncStatus)">
                {{ scope.row.syncStatus }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="startTime" label="开始时间" width="180">
            <template #default="scope">
              {{ formatDateTime(scope.row.startTime) }}
            </template>
          </el-table-column>
          <el-table-column prop="totalRecords" label="总记录数" width="100"></el-table-column>
          <el-table-column prop="successRecords" label="成功数" width="100"></el-table-column>
          <el-table-column prop="failedRecords" label="失败数" width="100"></el-table-column>
        </el-table>
      </el-card>
    </div>

    <!-- 同步记录历史 -->
    <div class="sync-history">
      <el-card title="同步记录历史">
        <template #header>
          <span>同步记录历史</span>
          <el-button 
            style="float: right; padding: 3px 0" 
            type="text" 
            @click="loadSyncRecords">
            刷新
          </el-button>
        </template>
        <el-table :data="syncRecords" v-loading="syncRecordsLoading">
          <el-table-column prop="id" label="记录ID" width="80"></el-table-column>
          <el-table-column prop="syncType" label="同步类型" width="120"></el-table-column>
          <el-table-column prop="syncStrategy" label="同步策略" width="120"></el-table-column>
          <el-table-column prop="syncStatus" label="状态" width="100">
            <template #default="scope">
              <el-tag :type="getStatusType(scope.row.syncStatus)">
                {{ scope.row.syncStatus }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="startTime" label="开始时间" width="180">
            <template #default="scope">
              {{ formatDateTime(scope.row.startTime) }}
            </template>
          </el-table-column>
          <el-table-column prop="endTime" label="结束时间" width="180">
            <template #default="scope">
              {{ scope.row.endTime ? formatDateTime(scope.row.endTime) : '-' }}
            </template>
          </el-table-column>
          <el-table-column prop="totalRecords" label="总记录数" width="100"></el-table-column>
          <el-table-column prop="successRecords" label="成功数" width="100"></el-table-column>
          <el-table-column prop="failedRecords" label="失败数" width="100"></el-table-column>
          <el-table-column label="操作" width="120">
            <template #default="scope">
              <el-button 
                type="text" 
                size="small" 
                @click="viewSyncRecord(scope.row)">
                查看详情
              </el-button>
            </template>
          </el-table-column>
        </el-table>
        <el-pagination
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          :current-page="currentPage"
          :page-sizes="[10, 20, 50, 100]"
          :page-size="pageSize"
          layout="total, sizes, prev, pager, next, jumper"
          :total="totalRecords">
        </el-pagination>
      </el-card>
    </div>
  </div>
</template>

<script>
import axios from 'axios'

export default {
  name: 'EnoviaSync',
  data() {
    return {
      fullSyncLoading: false,
      incrementalSyncLoading: false,
      clearCacheLoading: false,
      runningTasksLoading: false,
      syncRecordsLoading: false,
      statistics: {},
      runningTasks: [],
      syncRecords: [],
      currentPage: 1,
      pageSize: 20,
      totalRecords: 0
    }
  },
  mounted() {
    this.loadStatistics()
    this.loadRunningTasks()
    this.loadSyncRecords()
  },
  methods: {
    async startFullSync() {
      this.fullSyncLoading = true
      try {
        const response = await axios.post('/api/enovia/sync/full')
        this.$message.success(response.data)
        this.loadRunningTasks()
      } catch (error) {
        this.$message.error('启动全量同步失败: ' + error.response?.data || error.message)
      } finally {
        this.fullSyncLoading = false
      }
    },
    async startIncrementalSync() {
      this.incrementalSyncLoading = true
      try {
        const response = await axios.post('/api/enovia/sync/incremental')
        this.$message.success(response.data)
        this.loadRunningTasks()
      } catch (error) {
        this.$message.error('启动增量同步失败: ' + error.response?.data || error.message)
      } finally {
        this.incrementalSyncLoading = false
      }
    },
    async clearCache() {
      this.clearCacheLoading = true
      try {
        const response = await axios.post('/api/enovia/sync/clear-cache')
        this.$message.success(response.data)
      } catch (error) {
        this.$message.error('清除缓存失败: ' + error.response?.data || error.message)
      } finally {
        this.clearCacheLoading = false
      }
    },
    async loadStatistics() {
      try {
        const response = await axios.get('/api/enovia/sync/statistics')
        this.statistics = response.data
      } catch (error) {
        console.error('加载统计信息失败:', error)
      }
    },
    async loadRunningTasks() {
      this.runningTasksLoading = true
      try {
        const response = await axios.get('/api/enovia/sync/running')
        this.runningTasks = response.data
      } catch (error) {
        console.error('加载运行任务失败:', error)
      } finally {
        this.runningTasksLoading = false
      }
    },
    async loadSyncRecords() {
      this.syncRecordsLoading = true
      try {
        const response = await axios.get('/api/enovia/sync/records', {
          params: {
            page: this.currentPage - 1,
            size: this.pageSize
          }
        })
        this.syncRecords = response.data.content
        this.totalRecords = response.data.totalElements
      } catch (error) {
        console.error('加载同步记录失败:', error)
      } finally {
        this.syncRecordsLoading = false
      }
    },
    handleSizeChange(val) {
      this.pageSize = val
      this.loadSyncRecords()
    },
    handleCurrentChange(val) {
      this.currentPage = val
      this.loadSyncRecords()
    },
    viewSyncRecord(record) {
      // 显示同步记录详情
      this.$alert(JSON.stringify(record, null, 2), '同步记录详情', {
        confirmButtonText: '确定'
      })
    },
    getStatusType(status) {
      switch (status) {
        case 'SUCCESS': return 'success'
        case 'FAILED': return 'danger'
        case 'RUNNING': return 'warning'
        case 'PENDING': return 'info'
        default: return ''
      }
    },
    formatDateTime(dateTime) {
      if (!dateTime) return '-'
      return new Date(dateTime).toLocaleString('zh-CN')
    },
    formatDuration(milliseconds) {
      if (!milliseconds) return '-'
      const seconds = Math.floor(milliseconds / 1000)
      const minutes = Math.floor(seconds / 60)
      const hours = Math.floor(minutes / 60)
      
      if (hours > 0) {
        return `${hours}小时${minutes % 60}分钟${seconds % 60}秒`
      } else if (minutes > 0) {
        return `${minutes}分钟${seconds % 60}秒`
      } else {
        return `${seconds}秒`
      }
    }
  }
}
</script>

<style scoped>
.enovia-sync {
  padding: 20px;
}

.page-header {
  margin-bottom: 20px;
}

.page-header h1 {
  margin: 0 0 10px 0;
  color: #303133;
}

.page-header p {
  margin: 0;
  color: #606266;
}

.sync-controls,
.sync-statistics,
.running-tasks,
.sync-history {
  margin-bottom: 20px;
}

.control-buttons {
  display: flex;
  gap: 10px;
}

.stat-item {
  text-align: center;
  padding: 20px;
  border: 1px solid #EBEEF5;
  border-radius: 4px;
}

.stat-value {
  font-size: 32px;
  font-weight: bold;
  color: #303133;
  margin-bottom: 8px;
}

.stat-value.success {
  color: #67C23A;
}

.stat-value.error {
  color: #F56C6C;
}

.stat-label {
  font-size: 14px;
  color: #909399;
}

.last-sync {
  margin-top: 20px;
  padding-top: 20px;
  border-top: 1px solid #EBEEF5;
}

.last-sync p {
  margin: 5px 0;
  color: #606266;
}
</style>
