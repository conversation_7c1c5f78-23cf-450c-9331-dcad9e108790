-- 初始化序列配置
INSERT INTO sequence_config (SEQUENCE_NAME, CURRENT_VALUE, PREFIX, SUFFIX, INITIAL_VALUE) VALUES
('EL_SEQ', 1, 'EL', NULL, 1),
('ME_SEQ', 1, 'ME', NULL, 1),
('RM_SEQ', 1, 'RM', NULL, 1),
('FG_SEQ', 1, 'FG', NULL, 1),
('SFG_SEQ', 1, 'SFG', NULL, 1),
('PKG_SEQ', 1, 'PKG', NULL, 1);

-- 初始化物料分类
INSERT INTO MATERIAL_CLASSIFICATION (NAME, CODE, PARENT_ID, DESCRIPTION) VALUES
('电子元件', 'EL', NULL, '电子类物料'),
('机械零件', 'ME', NULL, '机械类物料'),
('原材料', 'RM', NULL, '原材料类'),
('成品', 'FG', NULL, '成品类'),
('半成品', 'SFG', NULL, '半成品类'),
('包装材料', 'PKG', NULL, '包装材料类');

-- 初始化分类属性定义
-- 电子元件属性
INSERT INTO CLASSIFICATION_ATTR_DEF (CLASSIFICATION_ID, ATTRIBUTE_NAME, ATTRIBUTE_CODE, DATA_TYPE, IS_REQUIRED, DESCRIPTION) 
SELECT ID, '电压值', 'VOLTAGE', 'STRING', 'Y', '电压值属性'
FROM MATERIAL_CLASSIFICATION WHERE CODE = 'EL';

INSERT INTO CLASSIFICATION_ATTR_DEF (CLASSIFICATION_ID, ATTRIBUTE_NAME, ATTRIBUTE_CODE, DATA_TYPE, IS_REQUIRED, DESCRIPTION)
SELECT ID, '电阻值', 'RESISTANCE', 'STRING', 'N', '电阻值属性'
FROM MATERIAL_CLASSIFICATION WHERE CODE = 'EL';

INSERT INTO CLASSIFICATION_ATTR_DEF (CLASSIFICATION_ID, ATTRIBUTE_NAME, ATTRIBUTE_CODE, DATA_TYPE, IS_REQUIRED, DESCRIPTION)
SELECT ID, '容值', 'CAPACITANCE', 'STRING', 'N', '电容值属性'
FROM MATERIAL_CLASSIFICATION WHERE CODE = 'EL';

-- 机械零件属性
INSERT INTO CLASSIFICATION_ATTR_DEF (CLASSIFICATION_ID, ATTRIBUTE_NAME, ATTRIBUTE_CODE, DATA_TYPE, IS_REQUIRED, DESCRIPTION)
SELECT ID, '尺寸规格', 'SIZE', 'STRING', 'Y', '尺寸规格属性'
FROM MATERIAL_CLASSIFICATION WHERE CODE = 'ME';

INSERT INTO CLASSIFICATION_ATTR_DEF (CLASSIFICATION_ID, ATTRIBUTE_NAME, ATTRIBUTE_CODE, DATA_TYPE, IS_REQUIRED, DESCRIPTION)
SELECT ID, '材料类型', 'MATERIAL', 'STRING', 'Y', '材料类型属性'
FROM MATERIAL_CLASSIFICATION WHERE CODE = 'ME';

INSERT INTO CLASSIFICATION_ATTR_DEF (CLASSIFICATION_ID, ATTRIBUTE_NAME, ATTRIBUTE_CODE, DATA_TYPE, IS_REQUIRED, DESCRIPTION)
SELECT ID, '颜色', 'COLOR', 'STRING', 'N', '颜色属性'
FROM MATERIAL_CLASSIFICATION WHERE CODE = 'ME';

-- 原材料属性
INSERT INTO CLASSIFICATION_ATTR_DEF (CLASSIFICATION_ID, ATTRIBUTE_NAME, ATTRIBUTE_CODE, DATA_TYPE, IS_REQUIRED, DESCRIPTION) 
SELECT ID, '材料', 'MATERIAL', 'STRING', 'Y', '材料属性'
FROM MATERIAL_CLASSIFICATION WHERE CODE = 'RM';

INSERT INTO CLASSIFICATION_ATTR_DEF (CLASSIFICATION_ID, ATTRIBUTE_NAME, ATTRIBUTE_CODE, DATA_TYPE, IS_REQUIRED, DESCRIPTION)
SELECT ID, '规格', 'SPEC', 'STRING', 'Y', '规格属性'
FROM MATERIAL_CLASSIFICATION WHERE CODE = 'RM';

-- 初始化编码规则
INSERT INTO CODING_RULE (RULE_NAME, DESCRIPTION, OBJECT_TYPE_CODE, PRIORITY, ENABLE_CHECK_DIGIT, IS_ACTIVE) VALUES
('电子元件编码规则', '用于生成电子元件编码', 'MATERIAL', 1, 'Y', 'Y'),
('机械零件编码规则', '用于生成机械零件编码', 'MATERIAL', 2, 'Y', 'Y'),
('原材料编码规则', '用于生成原材料编码', 'MATERIAL', 3, 'Y', 'Y');

-- 初始化编码规则段 - 电子元件
INSERT INTO CODING_RULE_SEGMENT (RULE_ID, SEGMENT_ORDER, SEGMENT_TYPE, FIXED_VALUE, LENGTH, DESCRIPTION)
SELECT ID, 1, 'CLASSIFICATION', 'EL', 2, '分类段'
FROM CODING_RULE WHERE RULE_NAME = '电子元件编码规则';

INSERT INTO CODING_RULE_SEGMENT (RULE_ID, SEGMENT_ORDER, SEGMENT_TYPE, ATTRIBUTE_CODE, LENGTH, DESCRIPTION)
SELECT ID, 2, 'ATTRIBUTE', 'VOLTAGE', 2, '电压段'
FROM CODING_RULE WHERE RULE_NAME = '电子元件编码规则';

INSERT INTO CODING_RULE_SEGMENT (RULE_ID, SEGMENT_ORDER, SEGMENT_TYPE, SEQUENCE_NAME, LENGTH, DESCRIPTION)
SELECT ID, 3, 'SEQUENCE', 'EL_SEQ', 4, '流水号'
FROM CODING_RULE WHERE RULE_NAME = '电子元件编码规则';

-- 初始化编码规则段 - 机械零件
INSERT INTO CODING_RULE_SEGMENT (RULE_ID, SEGMENT_ORDER, SEGMENT_TYPE, FIXED_VALUE, LENGTH, DESCRIPTION)
SELECT ID, 1, 'CLASSIFICATION', 'ME', 2, '分类段'
FROM CODING_RULE WHERE RULE_NAME = '机械零件编码规则';

INSERT INTO CODING_RULE_SEGMENT (RULE_ID, SEGMENT_ORDER, SEGMENT_TYPE, ATTRIBUTE_CODE, LENGTH, DESCRIPTION)
SELECT ID, 2, 'ATTRIBUTE', 'MATERIAL', 2, '材料段'
FROM CODING_RULE WHERE RULE_NAME = '机械零件编码规则';

INSERT INTO CODING_RULE_SEGMENT (RULE_ID, SEGMENT_ORDER, SEGMENT_TYPE, SEQUENCE_NAME, LENGTH, DESCRIPTION)
SELECT ID, 3, 'SEQUENCE', 'ME_SEQ', 4, '流水号'
FROM CODING_RULE WHERE RULE_NAME = '机械零件编码规则';

-- 初始化编码规则段 - 原材料
INSERT INTO CODING_RULE_SEGMENT (RULE_ID, SEGMENT_ORDER, SEGMENT_TYPE, FIXED_VALUE, LENGTH, DESCRIPTION)
SELECT ID, 1, 'CLASSIFICATION', 'RM', 2, '分类段'
FROM CODING_RULE WHERE RULE_NAME = '原材料编码规则';

INSERT INTO CODING_RULE_SEGMENT (RULE_ID, SEGMENT_ORDER, SEGMENT_TYPE, ATTRIBUTE_CODE, LENGTH, DESCRIPTION)
SELECT ID, 2, 'ATTRIBUTE', 'MATERIAL', 2, '材料段'
FROM CODING_RULE WHERE RULE_NAME = '原材料编码规则';

INSERT INTO CODING_RULE_SEGMENT (RULE_ID, SEGMENT_ORDER, SEGMENT_TYPE, SEQUENCE_NAME, LENGTH, DESCRIPTION)
SELECT ID, 3, 'SEQUENCE', 'RM_SEQ', 4, '流水号'
FROM CODING_RULE WHERE RULE_NAME = '原材料编码规则'; 