-- Enovia 数据同步相关表结构

-- ----------------------------
-- Table structure for enovia_type
-- ----------------------------
DROP TABLE IF EXISTS `enovia_type`;
CREATE TABLE `enovia_type` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `type_name` varchar(255) NOT NULL COMMENT 'Type名称',
  `display_name` varchar(255) DEFAULT NULL COMMENT '显示名称',
  `parent_type` varchar(255) DEFAULT NULL COMMENT '父类型名称',
  `is_abstract` tinyint(1) DEFAULT '0' COMMENT '是否为抽象类型',
  `is_hidden` tinyint(1) DEFAULT '0' COMMENT '是否为隐藏类型',
  `description` text COMMENT '描述',
  `icon_name` varchar(255) DEFAULT NULL COMMENT '图标名称',
  `sync_status` enum('PENDING','SYNCING','SUCCESS','FAILED') DEFAULT 'PENDING' COMMENT '同步状态',
  `last_sync_time` datetime DEFAULT NULL COMMENT '最后同步时间',
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_type_name` (`type_name`),
  KEY `idx_parent_type` (`parent_type`),
  KEY `idx_sync_status` (`sync_status`),
  KEY `idx_last_sync_time` (`last_sync_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='Enovia类型表';

-- ----------------------------
-- Table structure for enovia_type_attribute
-- ----------------------------
DROP TABLE IF EXISTS `enovia_type_attribute`;
CREATE TABLE `enovia_type_attribute` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `type_id` bigint NOT NULL COMMENT '关联的类型ID',
  `attribute_name` varchar(255) NOT NULL COMMENT '属性名称',
  `display_name` varchar(255) DEFAULT NULL COMMENT '显示名称',
  `attribute_type` varchar(100) NOT NULL COMMENT '属性类型',
  `data_type` varchar(50) DEFAULT NULL COMMENT '数据类型',
  `max_length` int DEFAULT NULL COMMENT '最大长度',
  `is_required` tinyint(1) DEFAULT '0' COMMENT '是否必填',
  `is_multivalue` tinyint(1) DEFAULT '0' COMMENT '是否多值',
  `is_hidden` tinyint(1) DEFAULT '0' COMMENT '是否隐藏',
  `default_value` varchar(500) DEFAULT NULL COMMENT '默认值',
  `value_range` text COMMENT '值范围(JSON格式)',
  `description` text COMMENT '描述',
  `sync_status` enum('PENDING','SYNCING','SUCCESS','FAILED') DEFAULT 'PENDING' COMMENT '同步状态',
  `last_sync_time` datetime DEFAULT NULL COMMENT '最后同步时间',
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_type_attribute` (`type_id`, `attribute_name`),
  KEY `idx_attribute_name` (`attribute_name`),
  KEY `idx_sync_status` (`sync_status`),
  KEY `idx_last_sync_time` (`last_sync_time`),
  CONSTRAINT `fk_type_attribute_type` FOREIGN KEY (`type_id`) REFERENCES `enovia_type` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='Enovia类型属性表';

-- ----------------------------
-- Table structure for enovia_sync_record
-- ----------------------------
DROP TABLE IF EXISTS `enovia_sync_record`;
CREATE TABLE `enovia_sync_record` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `sync_type` enum('TYPES','CLASSIFICATIONS','ATTRIBUTES','ALL') NOT NULL COMMENT '同步类型',
  `sync_strategy` enum('FULL','INCREMENTAL') NOT NULL COMMENT '同步策略',
  `sync_status` enum('PENDING','RUNNING','SUCCESS','FAILED','CANCELLED') NOT NULL COMMENT '同步状态',
  `start_time` datetime NOT NULL COMMENT '开始时间',
  `end_time` datetime DEFAULT NULL COMMENT '结束时间',
  `total_records` int DEFAULT '0' COMMENT '总记录数',
  `success_records` int DEFAULT '0' COMMENT '成功记录数',
  `failed_records` int DEFAULT '0' COMMENT '失败记录数',
  `skipped_records` int DEFAULT '0' COMMENT '跳过记录数',
  `error_message` text COMMENT '错误信息',
  `sync_details` text COMMENT '同步详情(JSON格式)',
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `idx_sync_type` (`sync_type`),
  KEY `idx_sync_status` (`sync_status`),
  KEY `idx_start_time` (`start_time`),
  KEY `idx_end_time` (`end_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='Enovia同步记录表';

-- ----------------------------
-- 扩展现有表结构，添加同步相关字段
-- ----------------------------

-- 为 material_classification 表添加同步字段
ALTER TABLE `material_classification` 
ADD COLUMN `enovia_id` varchar(255) DEFAULT NULL COMMENT 'Enovia中的分类ID',
ADD COLUMN `sync_status` enum('PENDING','SYNCING','SUCCESS','FAILED') DEFAULT 'PENDING' COMMENT '同步状态',
ADD COLUMN `last_sync_time` datetime DEFAULT NULL COMMENT '最后同步时间',
ADD INDEX `idx_enovia_id` (`enovia_id`),
ADD INDEX `idx_sync_status` (`sync_status`),
ADD INDEX `idx_last_sync_time` (`last_sync_time`);

-- 为 classification_attr_def 表添加同步字段
ALTER TABLE `classification_attr_def` 
ADD COLUMN `enovia_attribute_name` varchar(255) DEFAULT NULL COMMENT 'Enovia中的属性名称',
ADD COLUMN `sync_status` enum('PENDING','SYNCING','SUCCESS','FAILED') DEFAULT 'PENDING' COMMENT '同步状态',
ADD COLUMN `last_sync_time` datetime DEFAULT NULL COMMENT '最后同步时间',
ADD INDEX `idx_enovia_attribute_name` (`enovia_attribute_name`),
ADD INDEX `idx_sync_status` (`sync_status`),
ADD INDEX `idx_last_sync_time` (`last_sync_time`);

-- ----------------------------
-- 初始化同步配置数据
-- ----------------------------

-- 插入一些示例的 Enovia 类型数据（可选）
INSERT INTO `enovia_type` (`type_name`, `display_name`, `parent_type`, `is_abstract`, `is_hidden`, `description`) VALUES
('Part', '零件', NULL, 0, 0, 'Enovia系统中的零件类型'),
('Document', '文档', NULL, 0, 0, 'Enovia系统中的文档类型'),
('ECO', '工程变更单', NULL, 0, 0, 'Enovia系统中的工程变更单类型'),
('Person', '人员', NULL, 0, 0, 'Enovia系统中的人员类型'),
('Organization', '组织', NULL, 0, 0, 'Enovia系统中的组织类型');

-- 插入一条初始同步记录
INSERT INTO `enovia_sync_record` (`sync_type`, `sync_strategy`, `sync_status`, `start_time`, `total_records`, `success_records`, `failed_records`, `skipped_records`) VALUES
('ALL', 'FULL', 'PENDING', NOW(), 0, 0, 0, 0);
